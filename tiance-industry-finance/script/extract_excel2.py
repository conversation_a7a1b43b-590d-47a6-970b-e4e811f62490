﻿import openpyxl
import pandas as pd

def extract_links_from_excel(file_path, sheet_name):
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook[sheet_name]
    
    extracted_data = []
    header = [cell.value for cell in sheet[1]] # Get header row
    title_col_index = -1
    type_col_index = -1
    
    try:
        title_col_index = header.index("来源标题")
        type_col_index = header.index("来源详情（如有）")
        # publish_col_index = header.index("发布者")
        # publish_time_index = header.index("发布日期")
        # localtion_index = header.index("区域")
    except ValueError as e:
        print(f"Error: Column not found in the Excel sheet: {e}")
        return []

    for row_index in range(2, sheet.max_row + 1): # Start from the second row to skip header
        title_cell = sheet.cell(row=row_index, column=title_col_index + 1) # openpyxl is 1-indexed
        type_cell = sheet.cell(row=row_index, column=type_col_index + 1)

        title = title_cell.value
        url = type_cell.value
        if type_cell.hyperlink:
            url = type_cell.hyperlink.target
        # if not url.find("https"):
        #     url=None
        if title and url and not url.find("http"):
            # Sanitize title for use as filename
            sanitized_title = str(title).replace("/", "_").replace("\\", "_").replace(":", "_").replace("*", "_").replace("?", "_").replace("\"", "_").replace("<", "_").replace(">", "_").replace("|", "_")
            extracted_data.append({"title": sanitized_title, "url": url})
            
    return extracted_data

if __name__ == '__main__':
    excel_file = './广东省三条产业链-5.xlsx'
    sheet_name = '数据源'
    extracted_links_and_types = extract_links_from_excel(excel_file, sheet_name)
    
    # Save the extracted data to a file for later use
    with open('extracted_data_ver2.txt', 'w', encoding='utf-8') as f:
        for item in extracted_links_and_types:
            f.write(f"{item['title']},{item['url']}\n")
    print(f"Extracted {len(extracted_links_and_types)} links and types and saved to extracted_data.txt")


