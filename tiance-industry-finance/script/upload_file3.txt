--- First processing of files ---
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.pdf
跳过更新文件 '广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.json' (ID: ef25c60942f9e80328b4781c9aa72184) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.pdf
跳过更新文件 '优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.json' (ID: eed5a54773c627165ef7e6168b7850af) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省现代化海洋牧场发展总体规划（2024-2035年）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省现代化海洋牧场发展总体规划（2024-2035年）》.pdf
跳过更新文件 '《广东省现代化海洋牧场发展总体规划（2024-2035年）》.json' (ID: 7dac3605d86bfc67ee1ac34d15c9a21b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
成功上传文件 'downloaded_pdfs/广东海洋经济发展报告（2023）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济发展报告（2023）.pdf。
跳过更新文件 '广东海洋经济发展报告（2023）.json' (ID: e07e1aa09987c5d399e34780e26f3573) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《珠海市支持低空经济高质量发展的若干措施》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《珠海市支持低空经济高质量发展的若干措施》.pdf
跳过更新文件 '《珠海市支持低空经济高质量发展的若干措施》.json' (ID: 20e1b3628420f6737eb393ff94528c4c) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024年度部门整体支出绩效自评报告.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024年度部门整体支出绩效自评报告.json'。
跳过更新文件 '2024年度部门整体支出绩效自评报告.json' (ID: 9c756aab1c612362da40d9671b3fe4a0) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.json' (ID: e84f9c1d7a939485bec4bf3c91970559) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
成功上传文件 'downloaded_pdfs/2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.pdf。
跳过更新文件 '2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.json' (ID: 7880b85c07e55cb418fce73c925c5be0) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.pdf
跳过更新文件 '《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.json' (ID: ec6be968a275cbb23dc6f06bda60e8ae) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.json' (ID: 4597e68aa0f4cc082d418d8e374c2166) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.pdf
跳过更新文件 '投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.json' (ID: cdc056b718ffc201131445ad5ba2eb39) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.pdf
跳过更新文件 '天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.json' (ID: d460a8baf5ac583aeb4db70c9ae1b0ad) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.json' (ID: 850b4f1bc3dbf05aed923de5e3b81c31) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .pdf
跳过更新文件 '广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .json' (ID: 88c3e4faea0d94feb7e36be2b15bb7a5) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州推进新能源与储能产业创新发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州推进新能源与储能产业创新发展.pdf
跳过更新文件 '广州推进新能源与储能产业创新发展.json' (ID: ba09b32b1720265deaebfd91b5f3120e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024低空经济研究报告.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024低空经济研究报告.json'。
成功插入文件 '2024低空经济研究报告.json' 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.pdf
跳过更新文件 '电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.json' (ID: c68269b783f29cfb749d6aeea4453c8e) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源汽车上中下游产业链图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源汽车上中下游产业链图谱.pdf
跳过更新文件 '新能源汽车上中下游产业链图谱.json' (ID: 9414e1664804bf5d298acd454b00663b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/我国新能源汽车产业新形势及培育模式差异研究.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/我国新能源汽车产业新形势及培育模式差异研究.pdf
跳过更新文件 '我国新能源汽车产业新形势及培育模式差异研究.json' (ID: 8fe2a09ae2993b36882211620b2d7b32) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.pdf
跳过更新文件 '大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.json' (ID: 7d8cb7bdb9c266199fc27078225eb419) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/快速引领渔业产业链转型升级.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/快速引领渔业产业链转型升级.pdf
跳过更新文件 '快速引领渔业产业链转型升级.json' (ID: 293739a09e922753a1734dbbf844e3ff) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《渔业发展补助资金管理办法》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《渔业发展补助资金管理办法》.pdf
跳过更新文件 '《渔业发展补助资金管理办法》.json' (ID: 7d69707d54aeb27a891092bb9065793a) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.json'。
跳过更新文件 '开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.json' (ID: 382eddd67c532cf162b14ebfb0a09a69) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省新能源产业专利统计分析报告.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/广东省新能源产业专利统计分析报告.pdf
跳过更新文件 '广东省新能源产业专利统计分析报告.json' (ID: 99b56b86850ee7db1091f939b063d0f8) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/图解产业链：新能源汽车.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/图解产业链：新能源汽车.pdf
跳过更新文件 '图解产业链：新能源汽车.json' (ID: 5d1570588fc130b21285d62fddea88e2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.pdf
跳过更新文件 '解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.json' (ID: 7c2f87c1f91c7c13935fbb818b474b16) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.pdf
跳过更新文件 '广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.json' (ID: 34d2d3bbe7f2175e589962f7691663f2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.json'。
跳过更新文件 '东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.json' (ID: 358d7041dc182c2c1096c19e7dea367c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.json'。
跳过更新文件 '低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.json' (ID: 184dbd9e220472512d69b43f876cea1b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.json' (ID: 97fcfdf6efeaff5399ece17ee02597ed) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.pdf
跳过更新文件 '为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.json' (ID: cbd95b70e1bc25cced290fe7e5e89550) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2025年广东新能源产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2025年广东新能源产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2025年广东新能源产业链全景图谱.json' (ID: 8cb91344f70e798794d7a5dd53ab0b46) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.pdf
跳过更新文件 '2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.json' (ID: 920cc636adf58ee64710f667488dc375) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.pdf
跳过更新文件 '科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.json' (ID: c2acaee48ebf99378a8ff3912bf9a9a2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤港澳大湾区发展形成了两条“低空经济交叉带”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/粤港澳大湾区发展形成了两条“低空经济交叉带”.pdf
跳过更新文件 '粤港澳大湾区发展形成了两条“低空经济交叉带”.json' (ID: fe9b434c5b1ebbbdb42695bf0d7b6f96) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省低空经济产业发展战略研究项目启动.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省低空经济产业发展战略研究项目启动.pdf
跳过更新文件 '广东省低空经济产业发展战略研究项目启动.json' (ID: 285a1789ee23823295f8ce76212bad11) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/共生理论下海洋渔业“双链”融合机制、演化模式与路径.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/共生理论下海洋渔业“双链”融合机制、演化模式与路径.pdf
跳过更新文件 '共生理论下海洋渔业“双链”融合机制、演化模式与路径.json' (ID: 3e2749046a59f73bd0a4161a3b18d69b) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/向“上”发力！南沙低空经济又有新动作.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/向“上”发力！南沙低空经济又有新动作.pdf
跳过更新文件 '向“上”发力！南沙低空经济又有新动作.json' (ID: 07842265cf52ea3d9f5e078bccccaa13) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 上传/更新文件 'downloaded_pdfs/广东省发展和改革委员会 广东省能源局 广东省科学技术厅 广东省工业和信息化厅 广东省自然资源厅 广东省生态环境厅关于印发广东省培育新能源战略性新兴产业集群行动计划（2023-2025年）的通知.pdf' 到 MinIO 失败: HTTPConnectionPool(host='***********', port=9000): Max retries exceeded with url: /tiance-industry-finance/policy/2025/7/3/%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%8F%91%E5%B1%95%E5%92%8C%E6%94%B9%E9%9D%A9%E5%A7%94%E5%91%98%E4%BC%9A%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E8%83%BD%E6%BA%90%E5%B1%80%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E7%A7%91%E5%AD%A6%E6%8A%80%E6%9C%AF%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%B7%A5%E4%B8%9A%E5%92%8C%E4%BF%A1%E6%81%AF%E5%8C%96%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E8%87%AA%E7%84%B6%E8%B5%84%E6%BA%90%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E7%94%9F%E6%80%81%E7%8E%AF%E5%A2%83%E5%8E%85%E5%85%B3%E4%BA%8E%E5%8D%B0%E5%8F%91%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%9F%B9%E8%82%B2%E6%96%B0%E8%83%BD%E6%BA%90%E6%88%98%E7%95%A5%E6%80%A7%E6%96%B0%E5%85%B4%E4%BA%A7%E4%B8%9A%E9%9B%86%E7%BE%A4%E8%A1%8C%E5%8A%A8%E8%AE%A1%E5%88%92%EF%BC%882023-2025%E5%B9%B4%EF%BC%89%E7%9A%84%E9%80%9A%E7%9F%A5.pdf (Caused by ResponseError('too many 500 error responses'))。来自 JSON: '广东省发展和改革委员会 广东省能源局 广东省科学技术厅 广东省工业和信息化厅 广东省自然资源厅 广东省生态环境厅关于印发广东省培育新能源战略性新兴产业集群行动计划（2023-2025年）的通知.json'。
has igonre id :976151deb216765a830d062a1d656b2a
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋经济总量“29连冠”，多地成立海洋发展部门.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济总量“29连冠”，多地成立海洋发展部门.pdf
跳过更新文件 '广东海洋经济总量“29连冠”，多地成立海洋发展部门.json' (ID: 2f038c4dacb2174e54bb384f1f9ddeb4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源汽车产业链解析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/新能源汽车产业链解析.pdf
跳过更新文件 '新能源汽车产业链解析.json' (ID: 8e5e2fbcd61ab4eef175eaf7bd19a3cf) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.pdf
跳过更新文件 '潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.json' (ID: a31b5c9a0dd4c2bdb87f73aba63b9e8f) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/关于公布2024年广东省重点农业龙头企业名单的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/关于公布2024年广东省重点农业龙头企业名单的通知.pdf
跳过更新文件 '关于公布2024年广东省重点农业龙头企业名单的通知.json' (ID: 1326bf0289ce4cf2c095a0825aad6885) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/如何打造低空经济“广东样本”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/如何打造低空经济“广东样本”.pdf
跳过更新文件 '如何打造低空经济“广东样本”.json' (ID: 39aace4624e088030b99e8181ad64df1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.pdf
跳过更新文件 '两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.json' (ID: b7103d4c57df422987b53e24d85b5c85) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/洲明科技(300232)答投资者问.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/洲明科技(300232)答投资者问.pdf
跳过更新文件 '洲明科技(300232)答投资者问.json' (ID: 719b0f97823c1fc49f27cda1a2e4439f) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.pdf
跳过更新文件 '粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.json' (ID: 317ebe42f7c147a06d48424415c69e49) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2024年广东省水产养殖重点品种监测年度报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2024年广东省水产养殖重点品种监测年度报告.pdf
跳过更新文件 '2024年广东省水产养殖重点品种监测年度报告.json' (ID: 2893de5b2b497e6723cc2b4316c2bf97) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.json'。
跳过更新文件 '东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.json' (ID: 6c0355350fbf7c6183a2dfa6dc8a209b) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省海洋渔业资源养护补贴政策实施方案》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省海洋渔业资源养护补贴政策实施方案》.pdf
跳过更新文件 '《广东省海洋渔业资源养护补贴政策实施方案》.json' (ID: 7d1efc962647b56c336d2903553ee002) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市推动低空经济高质量发展若干措施.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市推动低空经济高质量发展若干措施.pdf
跳过更新文件 '广州市推动低空经济高质量发展若干措施.json' (ID: bdd688bbf8493b21cf728c138a646a8c) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/向海图强——海洋经济产业链分析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/向海图强——海洋经济产业链分析.pdf
跳过更新文件 '向海图强——海洋经济产业链分析.json' (ID: c1e1c0ba24e03d0d9233cc8e4026eda3) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.pdf
跳过更新文件 '【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.json' (ID: 9d52cc4cbc749bc36d923f3fd137c3d9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.pdf
跳过更新文件 '广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.json' (ID: bffbdef8c72de4e717c42e31ed97dde1) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2023年新能源汽车行业研究报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2023年新能源汽车行业研究报告.pdf
跳过更新文件 '2023年新能源汽车行业研究报告.json' (ID: 21054c4af8bb623e6de14aa1d959bbef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.pdf
跳过更新文件 '《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.json' (ID: cdb489d34fa24284f3f8c4fd47b86d73) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.pdf
跳过更新文件 '广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.json' (ID: 532bfdc4201117ff5a01831f875ad0b8) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.json' (ID: 77ff56869a54ba4cb78c77e09a761209) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.pdf
跳过更新文件 '《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.json' (ID: 8af6a9db22e2a9e9fb077f6b7ad44248) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.pdf
跳过更新文件 '低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.json' (ID: f9209efceca5d5178f6c1c322f9e189f) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/鳗鱼价格飙升背后的养殖困境！.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/鳗鱼价格飙升背后的养殖困境！.pdf
跳过更新文件 '鳗鱼价格飙升背后的养殖困境！.json' (ID: 1ea76bfe9787a18a7977a44823b65db9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/让低空经济“飞”进发展新空间.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/让低空经济“飞”进发展新空间.pdf
跳过更新文件 '让低空经济“飞”进发展新空间.json' (ID: 94eac88bc214bb98ee9124cc097606ed) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋经济发展报告（2024）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济发展报告（2024）.pdf
跳过更新文件 '广东海洋经济发展报告（2024）.json' (ID: bcb6837bcd946b2b1e388daf217a05d1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/Measurement and Evolution of High-quality Development Level of Marine Fishery in China.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/Measurement and Evolution of High-quality Development Level of Marine Fishery in China.pdf
跳过更新文件 'Measurement and Evolution of High-quality Development Level of Marine Fishery in China.json' (ID: 612510d200602fafbb0946308a2442ca) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.pdf
跳过更新文件 '你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.json' (ID: 7d020f25e1b43a65384bf24c7f0f5588) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济广东领飞，国资加速入场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济广东领飞，国资加速入场.pdf
跳过更新文件 '低空经济广东领飞，国资加速入场.json' (ID: 67e8d31965ad31207fb0d8daa11a1eb4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.pdf
跳过更新文件 '五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.json' (ID: ed051a29513a47b6333dd641df54b346) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.json' (ID: 7c381b5f4822636669066915c82420ac) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.pdf
跳过更新文件 '2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.json' (ID: 374a8f265c0b01b4a5b2185080706d91) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.json'。
跳过更新文件 '民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.json' (ID: e439bfd8a0cbd00c2fdc123fdd288121) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.pdf
跳过更新文件 '《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.json' (ID: f08ceaf6f3214910aa41dd7147d4b150) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.json' (ID: b670c6d0fb9e31fe41d186ddb6b5fa51) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.pdf
跳过更新文件 '买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.json' (ID: 7bd17d6c8b0fd142249e8f8c49ea7f98) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2009年度农业部远洋渔业企业资格企业名单.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2009年度农业部远洋渔业企业资格企业名单.pdf
跳过更新文件 '2009年度农业部远洋渔业企业资格企业名单.json' (ID: 0bc603051bc2b04329f0fb377a940843) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/远洋渔业-头豹词条报告系列.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/远洋渔业-头豹词条报告系列.pdf
跳过更新文件 '远洋渔业-头豹词条报告系列.json' (ID: ff367167522abe27c6bf1c0ca52fefca) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.json' (ID: 6cfe1399215e51274e9159043ac1faf4) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.pdf
跳过更新文件 '《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.json' (ID: e606194ee7aac45eb4d1faac1097f77f) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《深圳市罗湖区促进商旅文低空应用的若干措施》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《深圳市罗湖区促进商旅文低空应用的若干措施》.pdf
跳过更新文件 '《深圳市罗湖区促进商旅文低空应用的若干措施》.json' (ID: 684932dc370723cd07b06baf6d4c3d77) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.json'。
跳过更新文件 '开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.json' (ID: 8a81033abf60045759ef47426907049d) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.pdf
跳过更新文件 '江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.json' (ID: 7bf59618f1dfa2056b16b838256736b3) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.pdf
跳过更新文件 '新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.json' (ID: 048a61afeaef94b4a0189ab29a843036) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【专家观点】推动海洋渔业向信息化智能化转型.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/【专家观点】推动海洋渔业向信息化智能化转型.pdf
跳过更新文件 '【专家观点】推动海洋渔业向信息化智能化转型.json' (ID: 70ee470ab4a4fb02c67e1d085ffd997a) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.json'。
成功插入文件 '2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.json' 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.pdf
跳过更新文件 '东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.json' (ID: bbff9d37ad216ca0498a37b05a5864c2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.pdf
跳过更新文件 '低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.json' (ID: 30ea62cec458c81ba4f0d5ff2c78ea01) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.json' (ID: ff2e2fdf7638c79a06ae8c6633bee976) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省公布1183家省重点农业龙头企业名单.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省公布1183家省重点农业龙头企业名单.pdf
跳过更新文件 '广东省公布1183家省重点农业龙头企业名单.json' (ID: 8c40e0cd1bd09b225552430c7bfd3ce1) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/从新材料到低空经济，长三角企业前往广东寻找商机.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/从新材料到低空经济，长三角企业前往广东寻找商机.pdf
跳过更新文件 '从新材料到低空经济，长三角企业前往广东寻找商机.json' (ID: 58d52e0278051d12ffb4b35d4c9ccfbb) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/打造海洋渔业全产业链.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/打造海洋渔业全产业链.pdf
跳过更新文件 '打造海洋渔业全产业链.json' (ID: 4573c7f2ee9305f9eb275fe2a256b340) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济产业链梳理_低空领航东吴计算机王紫敬团队.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济产业链梳理_低空领航东吴计算机王紫敬团队.pdf
跳过更新文件 '低空经济产业链梳理_低空领航东吴计算机王紫敬团队.json' (ID: 9d4fdd2b9d236887f1ee90db957c392d) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济四项得分居全国之首.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济四项得分居全国之首.pdf
跳过更新文件 '广东低空经济四项得分居全国之首.json' (ID: bc2648525894d1f5866625605f0236bd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/掘金拉美蓝海市场，广东新能源产业链加快抱团出海.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/掘金拉美蓝海市场，广东新能源产业链加快抱团出海.pdf
跳过更新文件 '掘金拉美蓝海市场，广东新能源产业链加快抱团出海.json' (ID: 11c73caf29aa013407573997139d5e1e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/涉海企业 .pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/涉海企业 .pdf
跳过更新文件 '涉海企业 .json' (ID: 1e3317566fece6edcab9eaf9cbd63640) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025年广东新能源产业链分析.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2025年广东新能源产业链分析.pdf
跳过更新文件 '2025年广东新能源产业链分析.json' (ID: 71c0425ea0f681edcea5743f8557f64b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].pdf
跳过更新文件 '广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].json' (ID: e6f35d755bee415385df34f7d7dc30f1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/总投资6亿元！两大电池新能源项目在广东奠基.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/总投资6亿元！两大电池新能源项目在广东奠基.pdf
跳过更新文件 '总投资6亿元！两大电池新能源项目在广东奠基.json' (ID: c7eea7ccd2bd6937e3e725876d9ab228) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济发展条例.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市低空经济发展条例.pdf
跳过更新文件 '广州市低空经济发展条例.json' (ID: 45b98454bd950b23cf5e17c3c0fff44b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/开源北交所研究精选 _ 低空经济产业链北交所全梳理.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/开源北交所研究精选 _ 低空经济产业链北交所全梳理.pdf
跳过更新文件 '开源北交所研究精选 _ 低空经济产业链北交所全梳理.json' (ID: 92741f594aa6ae9b651540f0b09b4abc) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.pdf
跳过更新文件 '《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.json' (ID: 62395782c6ace9e0ba66c4f6372a808e) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.pdf
跳过更新文件 '2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.json' (ID: 6bebb0be5c598c82422a5a32dc1b27fd) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济迎风直上.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济迎风直上.pdf
跳过更新文件 '广东低空经济迎风直上.json' (ID: 9f5876c1887938ab278627ee51ad3f55) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.pdf
跳过更新文件 '第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.json' (ID: 9b2523fa9c84a0e3a3db4ffa8313f147) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省经济分析报告 2024年.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2024年.pdf
跳过更新文件 '广东省经济分析报告 2024年.json' (ID: 362d45cdc0fbc1c6e4284591c4540dea) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省“十三五”新能源产业发展规划研究.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/广东省“十三五”新能源产业发展规划研究.pdf
跳过更新文件 '广东省“十三五”新能源产业发展规划研究.json' (ID: 68215f2d4d887abc2603ec523727ce60) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2024年广州低空经济产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2024年广州低空经济产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2024年广州低空经济产业链全景图谱.json' (ID: 681f8f11630f670615745a8ce6cd017c) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.pdf
跳过更新文件 '制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.json' (ID: fc45d47721a54f78f1e1f3d222876468) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
成功上传文件 'downloaded_pdfs/广东省经济分析报告 2024年第一季度.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2024年第一季度.pdf。
跳过更新文件 '广东省经济分析报告 2024年第一季度.json' (ID: 0a95b6cf64f854a40cb4dfab01bf635a) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济发展实施方案.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市低空经济发展实施方案.pdf
跳过更新文件 '广州市低空经济发展实施方案.json' (ID: 87bba05458bb87d37d73eaf8bcfc3c41) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.pdf
跳过更新文件 '广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.json' (ID: 69eef0faa66c2addd18f6dbb082b8d04) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/汕尾市海洋养殖发展规划.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/汕尾市海洋养殖发展规划.pdf
跳过更新文件 '汕尾市海洋养殖发展规划.json' (ID: c7ca721ab1433db81edaa8ca4a6a9a33) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.pdf
跳过更新文件 '广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.json' (ID: 462618b1e123b047202144213ec94d27) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.json'。
跳过更新文件 '万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.json' (ID: 89ca9f96739060d31250599ece179916) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/中国船级社支持业界打造海洋渔业装备全产业链.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/中国船级社支持业界打造海洋渔业装备全产业链.pdf
跳过更新文件 '中国船级社支持业界打造海洋渔业装备全产业链.json' (ID: b8a342372d15c7a42e892c01e372da98) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.json' (ID: bc7cbd79e3359758720ace7173af9037) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.pdf
跳过更新文件 '集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.json' (ID: f3cbcdcc93cb7cc445fb98efbe290ade) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2019-2025年广东省能源市场运行态势及行业发展前景预测报告.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2019-2025年广东省能源市场运行态势及行业发展前景预测报告.pdf
跳过更新文件 '2019-2025年广东省能源市场运行态势及行业发展前景预测报告.json' (ID: b85e58fb792e3dd3c2698524eb7e748c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.pdf
跳过更新文件 '预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.json' (ID: 7984b91c0c98539d672e76eb509957ef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.pdf
跳过更新文件 '广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.json' (ID: fd9990215fcb32a1e9fb9f8da5b1ec29) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.pdf
跳过更新文件 '拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.json' (ID: 5252f4afc4015b09ceaa492b8ea97d53) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州新政助推智能网联新能源汽车产业发展.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州新政助推智能网联新能源汽车产业发展.pdf
跳过更新文件 '广州新政助推智能网联新能源汽车产业发展.json' (ID: 975abcf115ffea436f927090b7eddfdf) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/浙江海洋渔业产业链及其贡献度分析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/浙江海洋渔业产业链及其贡献度分析.pdf
跳过更新文件 '浙江海洋渔业产业链及其贡献度分析.json' (ID: 2debf5fc2c06d38b0faf9206e9f96667) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州新能源汽车产业链升级 项目从动工到试投产仅10个月.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州新能源汽车产业链升级 项目从动工到试投产仅10个月.pdf
跳过更新文件 '广州新能源汽车产业链升级 项目从动工到试投产仅10个月.json' (ID: e95d1dc942f4acb089425c376d4c6f10) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2022年渔业行业研究报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2022年渔业行业研究报告.pdf
跳过更新文件 '2022年渔业行业研究报告.json' (ID: 365d4255501e6123dad64408c6137f54) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.pdf
跳过更新文件 '粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.json' (ID: 6548768fbe70b589d3fbaa558e54462c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.json'。
跳过更新文件 '万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.json' (ID: dda2f4358d3d541e52f57ea4efd67064) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省推动低空经济高质量发展行动方案（2024—2026年）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省推动低空经济高质量发展行动方案（2024—2026年）.pdf
跳过更新文件 '广东省推动低空经济高质量发展行动方案（2024—2026年）.json' (ID: 17bcfb047442d923006e74f7d2c7c337) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/产业科技创融看广东丨低空经济链上企业超三成在广东.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/产业科技创融看广东丨低空经济链上企业超三成在广东.pdf
跳过更新文件 '产业科技创融看广东丨低空经济链上企业超三成在广东.json' (ID: 70d1fa543cd655d9bdfe3c219798b496) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2025年广东低空经济产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2025年广东低空经济产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2025年广东低空经济产业链全景图谱.json' (ID: fc4202d92f55be2951feba09a1582ec3) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.json'。
跳过更新文件 '湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.json' (ID: 1b91c8eda0e129ba8c9add5980249000) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.pdf
跳过更新文件 '广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.json' (ID: d342aff474136bc2e86cf69df4fffbe2) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.pdf
跳过更新文件 '广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.json' (ID: 08ae1a43953d69e3f884f802c9a9ffeb) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.pdf
跳过更新文件 '莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.json' (ID: 4b30e3bec0b24cc559b2a7927d2974fd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省渔业企业列表.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省渔业企业列表.pdf
跳过更新文件 '广东省渔业企业列表.json' (ID: 2eb9a15357f96dd4b43c24947e917b6e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济：来自南方的创新力量.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济：来自南方的创新力量.pdf
跳过更新文件 '广东低空经济：来自南方的创新力量.json' (ID: 4be4d8f43303114bd74e22cc4c7bed29) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/zf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: 'zf.json'。
跳过更新文件 'zf.json' (ID: 819bdbde26bea3dab1d223bf07d85fef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/逆全球化背景下的电车出口跟踪.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '逆全球化背景下的电车出口跟踪.json'。
跳过更新文件 '逆全球化背景下的电车出口跟踪.json' (ID: 2e71445e39c51159ae415532c1f380c3) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.pdf
跳过更新文件 '广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.json' (ID: ffedb4dedecc33fa6f742f60664c6e83) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.pdf
跳过更新文件 '中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.json' (ID: 5f13067f68922f8656f1bca1f3d4fed7) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东阳江海洋渔业经济得到长足发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东阳江海洋渔业经济得到长足发展.pdf
跳过更新文件 '广东阳江海洋渔业经济得到长足发展.json' (ID: abfeaf07b1257e4f3333d97368962974) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济发展调研报告发布，深穗企业数位列全国前二.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济发展调研报告发布，深穗企业数位列全国前二.pdf
跳过更新文件 '广东低空经济发展调研报告发布，深穗企业数位列全国前二.json' (ID: b8bfbf7dc8eb358db3526962ff26ae92) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
成功上传文件 'downloaded_pdfs/新能源汽车专业人才需求调研报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源汽车专业人才需求调研报告.pdf。
跳过更新文件 '新能源汽车专业人才需求调研报告.json' (ID: 02be550cc383b64d92acc9f258e83745) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '.json'。
跳过更新文件 '.json' (ID: 9d52cc4cbc749bc36d923f3fd137c3d9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省促进海洋经济高质量发展条例.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省促进海洋经济高质量发展条例.pdf
跳过更新文件 '广东省促进海洋经济高质量发展条例.json' (ID: d8a178316fc530a456af8dc6c652beba) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/变与不变——新能源汽车行业研究框架.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/变与不变——新能源汽车行业研究框架.pdf
跳过更新文件 '变与不变——新能源汽车行业研究框架.json' (ID: 26bc1e8911417b019a619281e227ab29) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.pdf
跳过更新文件 '政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.json' (ID: 638b242a64490f3ecbd6ca07227a7e5b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/海洋经济（二）——海洋渔业产业链全景.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/海洋经济（二）——海洋渔业产业链全景.pdf
跳过更新文件 '海洋经济（二）——海洋渔业产业链全景.json' (ID: a73cbe0fdbeb9cde9441cc885f8277a4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/珠海国资国企探索低空经济“珠海模式”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/珠海国资国企探索低空经济“珠海模式”.pdf
跳过更新文件 '珠海国资国企探索低空经济“珠海模式”.json' (ID: 8d685bcee4d5748fe64a2ad33e1ecdc5) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
成功上传文件 'downloaded_pdfs/低空经济发展研究报告（2024）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济发展研究报告（2024）.pdf。
跳过更新文件 '低空经济发展研究报告（2024）.json' (ID: 5c248585721aeea922671d262907cd3e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济综合实力与发展潜力全面评估报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州市低空经济综合实力与发展潜力全面评估报告.pdf
跳过更新文件 '广州市低空经济综合实力与发展潜力全面评估报告.json' (ID: db9f237b45b08717216e03d3f25c9584) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.pdf
跳过更新文件 '前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.json' (ID: b719738b009b5602f21b5e30a0de5afd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/湛江市现代化海洋牧场建设规划 （2023-2035年）.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/湛江市现代化海洋牧场建设规划 （2023-2035年）.pdf
跳过更新文件 '湛江市现代化海洋牧场建设规划 （2023-2035年）.json' (ID: e5d03b7570df3a4797dc149e7ee79a75) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.pdf
跳过更新文件 '《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.json' (ID: 2fa45a6563e71149cc41e5f5796529d4) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2021年中国新能源汽车换电市场－艾瑞.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2021年中国新能源汽车换电市场－艾瑞.pdf
跳过更新文件 '2021年中国新能源汽车换电市场－艾瑞.json' (ID: cf17b8255096b5ffb6c57ac06dca106e) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省经济分析报告 2025年第一季度.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2025年第一季度.pdf
跳过更新文件 '广东省经济分析报告 2025年第一季度.json' (ID: 006d1d0cdbc8ce5bbe43677bd7a0ce90) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .pdf
跳过更新文件 '习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .json' (ID: 01ef1fcd6f46170c738d7fedcc198eb4) 到 MongoDB 集合 'policy_label_info_new'。

处理完成。所有错误和警告信息已记录到 'error_logs/processing_errors_20250707_142439.txt'。

--- Attempting to process again, testing duplicate insertion/update logic ---
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.pdf
跳过更新文件 '广东现代化海洋牧场建设又有新进展 全省首个中欧海洋渔业产业创新园成功签约.json' (ID: ef25c60942f9e80328b4781c9aa72184) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.pdf
跳过更新文件 '优优绿能登陆创业板：开启充电技术新篇章，加速布局全球新能源市场.json' (ID: eed5a54773c627165ef7e6168b7850af) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省现代化海洋牧场发展总体规划（2024-2035年）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省现代化海洋牧场发展总体规划（2024-2035年）》.pdf
跳过更新文件 '《广东省现代化海洋牧场发展总体规划（2024-2035年）》.json' (ID: 7dac3605d86bfc67ee1ac34d15c9a21b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋经济发展报告（2023）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济发展报告（2023）.pdf
跳过更新文件 '广东海洋经济发展报告（2023）.json' (ID: e07e1aa09987c5d399e34780e26f3573) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《珠海市支持低空经济高质量发展的若干措施》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《珠海市支持低空经济高质量发展的若干措施》.pdf
跳过更新文件 '《珠海市支持低空经济高质量发展的若干措施》.json' (ID: 20e1b3628420f6737eb393ff94528c4c) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024年度部门整体支出绩效自评报告.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024年度部门整体支出绩效自评报告.json'。
跳过更新文件 '2024年度部门整体支出绩效自评报告.json' (ID: 9c756aab1c612362da40d9671b3fe4a0) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：奔驰固态电池展开路试，龙蟠科技子公司获LGES增资.pdf.json' (ID: e84f9c1d7a939485bec4bf3c91970559) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.pdf
跳过更新文件 '2024年中国低空经济报告——蓄势待飞，展翅万亿新赛道.json' (ID: 7880b85c07e55cb418fce73c925c5be0) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.pdf
跳过更新文件 '《农业农村部关于调整海洋伏季休渔制度的通告》（农业农村部通告〔2023〕1 号）.json' (ID: ec6be968a275cbb23dc6f06bda60e8ae) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：太蓝新能源首发车规级全固态电池，蔚来150kWh电池包量产下线.pdf.json' (ID: 4597e68aa0f4cc082d418d8e374c2166) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.pdf
跳过更新文件 '投资深圳 _ 一文看懂深圳市新能源汽车产业发展现状与投资机会前瞻.json' (ID: cdc056b718ffc201131445ad5ba2eb39) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.pdf
跳过更新文件 '天眼新知 _ “低空经济”蓄势起飞 万亿市场拉开时代帷幕.json' (ID: d460a8baf5ac583aeb4db70c9ae1b0ad) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：宁德时代向宝马供货大圆柱，StarPlus Energy或获美国政府贷款支持.pdf.json' (ID: 850b4f1bc3dbf05aed923de5e3b81c31) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .pdf
跳过更新文件 '广东多地竞逐低空“蓝海” 低空经济蓄势腾飞 .json' (ID: 88c3e4faea0d94feb7e36be2b15bb7a5) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州推进新能源与储能产业创新发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州推进新能源与储能产业创新发展.pdf
跳过更新文件 '广州推进新能源与储能产业创新发展.json' (ID: ba09b32b1720265deaebfd91b5f3120e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024低空经济研究报告.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024低空经济研究报告.json'。
跳过更新文件 '2024低空经济研究报告.json' (ID: c696f2968fd0d6e5c8fec973c3eff6e4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.pdf
跳过更新文件 '电力设备与新能源行业深度报告：AI动力打造固态电池发展新引擎.json' (ID: c68269b783f29cfb749d6aeea4453c8e) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源汽车上中下游产业链图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源汽车上中下游产业链图谱.pdf
跳过更新文件 '新能源汽车上中下游产业链图谱.json' (ID: 9414e1664804bf5d298acd454b00663b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/我国新能源汽车产业新形势及培育模式差异研究.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/我国新能源汽车产业新形势及培育模式差异研究.pdf
跳过更新文件 '我国新能源汽车产业新形势及培育模式差异研究.json' (ID: 8fe2a09ae2993b36882211620b2d7b32) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.pdf
跳过更新文件 '大唐汕头新能源公司：入围2024年无故障风电场管理成果名单.json' (ID: 7d8cb7bdb9c266199fc27078225eb419) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/快速引领渔业产业链转型升级.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/快速引领渔业产业链转型升级.pdf
跳过更新文件 '快速引领渔业产业链转型升级.json' (ID: 293739a09e922753a1734dbbf844e3ff) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《渔业发展补助资金管理办法》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《渔业发展补助资金管理办法》.pdf
跳过更新文件 '《渔业发展补助资金管理办法》.json' (ID: 7d69707d54aeb27a891092bb9065793a) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.json'。
跳过更新文件 '开源证券_北交所科技新产业跟踪第十八期：北京发布低空经济产业发展三年规划，北交所相关公司布局低空经济产业链.pdf.json' (ID: 382eddd67c532cf162b14ebfb0a09a69) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省新能源产业专利统计分析报告.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/广东省新能源产业专利统计分析报告.pdf
跳过更新文件 '广东省新能源产业专利统计分析报告.json' (ID: 99b56b86850ee7db1091f939b063d0f8) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/图解产业链：新能源汽车.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/图解产业链：新能源汽车.pdf
跳过更新文件 '图解产业链：新能源汽车.json' (ID: 5d1570588fc130b21285d62fddea88e2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.pdf
跳过更新文件 '解码“数据要素×”｜人工成本降低60%效益提升超50%，看广东如何以数据赋能海洋渔业转型升级.json' (ID: 7c2f87c1f91c7c13935fbb818b474b16) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.pdf
跳过更新文件 '广东出台全国首个海洋渔业全产业发展规划 构建现代化海洋牧场全产业链体系.json' (ID: 34d2d3bbe7f2175e589962f7691663f2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.json'。
跳过更新文件 '东证期货_新能源汽车产业链年度报告：全球新能源车市透视-动荡中前行.pdf.json' (ID: 358d7041dc182c2c1096c19e7dea367c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.json'。
跳过更新文件 '低空经济行业周报：（4 月第 2 周）广东省加速低空立法，各地对应用场景探索稳步推进.json' (ID: 184dbd9e220472512d69b43f876cea1b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链跟踪点评：3月新能源汽车销量环比显著增长.pdf.json' (ID: 97fcfdf6efeaff5399ece17ee02597ed) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.pdf
跳过更新文件 '为经济持续高飞注入新动能 广东低空经济发展率先“起飞”.json' (ID: cbd95b70e1bc25cced290fe7e5e89550) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2025年广东新能源产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2025年广东新能源产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2025年广东新能源产业链全景图谱.json' (ID: 8cb91344f70e798794d7a5dd53ab0b46) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.pdf
跳过更新文件 '2025年中国海洋渔业行业发展现状及趋势分析，资源禀赋+科学可持续发展，行业发展无穷尽也「图」.json' (ID: 920cc636adf58ee64710f667488dc375) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.pdf
跳过更新文件 '科技为翼 乘势启航 _ 第二十六届高交会低空经济与空天主题展蓄势待发.json' (ID: c2acaee48ebf99378a8ff3912bf9a9a2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤港澳大湾区发展形成了两条“低空经济交叉带”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/粤港澳大湾区发展形成了两条“低空经济交叉带”.pdf
跳过更新文件 '粤港澳大湾区发展形成了两条“低空经济交叉带”.json' (ID: fe9b434c5b1ebbbdb42695bf0d7b6f96) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省低空经济产业发展战略研究项目启动.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省低空经济产业发展战略研究项目启动.pdf
跳过更新文件 '广东省低空经济产业发展战略研究项目启动.json' (ID: 285a1789ee23823295f8ce76212bad11) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/共生理论下海洋渔业“双链”融合机制、演化模式与路径.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/共生理论下海洋渔业“双链”融合机制、演化模式与路径.pdf
跳过更新文件 '共生理论下海洋渔业“双链”融合机制、演化模式与路径.json' (ID: 3e2749046a59f73bd0a4161a3b18d69b) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/向“上”发力！南沙低空经济又有新动作.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/向“上”发力！南沙低空经济又有新动作.pdf
跳过更新文件 '向“上”发力！南沙低空经济又有新动作.json' (ID: 07842265cf52ea3d9f5e078bccccaa13) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 上传/更新文件 'downloaded_pdfs/广东省发展和改革委员会 广东省能源局 广东省科学技术厅 广东省工业和信息化厅 广东省自然资源厅 广东省生态环境厅关于印发广东省培育新能源战略性新兴产业集群行动计划（2023-2025年）的通知.pdf' 到 MinIO 失败: HTTPConnectionPool(host='***********', port=9000): Max retries exceeded with url: /tiance-industry-finance/policy/2025/7/3/%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%8F%91%E5%B1%95%E5%92%8C%E6%94%B9%E9%9D%A9%E5%A7%94%E5%91%98%E4%BC%9A%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E8%83%BD%E6%BA%90%E5%B1%80%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E7%A7%91%E5%AD%A6%E6%8A%80%E6%9C%AF%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%B7%A5%E4%B8%9A%E5%92%8C%E4%BF%A1%E6%81%AF%E5%8C%96%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E8%87%AA%E7%84%B6%E8%B5%84%E6%BA%90%E5%8E%85%20%E5%B9%BF%E4%B8%9C%E7%9C%81%E7%94%9F%E6%80%81%E7%8E%AF%E5%A2%83%E5%8E%85%E5%85%B3%E4%BA%8E%E5%8D%B0%E5%8F%91%E5%B9%BF%E4%B8%9C%E7%9C%81%E5%9F%B9%E8%82%B2%E6%96%B0%E8%83%BD%E6%BA%90%E6%88%98%E7%95%A5%E6%80%A7%E6%96%B0%E5%85%B4%E4%BA%A7%E4%B8%9A%E9%9B%86%E7%BE%A4%E8%A1%8C%E5%8A%A8%E8%AE%A1%E5%88%92%EF%BC%882023-2025%E5%B9%B4%EF%BC%89%E7%9A%84%E9%80%9A%E7%9F%A5.pdf (Caused by ResponseError('too many 500 error responses'))。来自 JSON: '广东省发展和改革委员会 广东省能源局 广东省科学技术厅 广东省工业和信息化厅 广东省自然资源厅 广东省生态环境厅关于印发广东省培育新能源战略性新兴产业集群行动计划（2023-2025年）的通知.json'。
has igonre id :976151deb216765a830d062a1d656b2a
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋经济总量“29连冠”，多地成立海洋发展部门.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济总量“29连冠”，多地成立海洋发展部门.pdf
跳过更新文件 '广东海洋经济总量“29连冠”，多地成立海洋发展部门.json' (ID: 2f038c4dacb2174e54bb384f1f9ddeb4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源汽车产业链解析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/新能源汽车产业链解析.pdf
跳过更新文件 '新能源汽车产业链解析.json' (ID: 8e5e2fbcd61ab4eef175eaf7bd19a3cf) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.pdf
跳过更新文件 '潮州市人民政府办公室关于成立潮州市推动低空经济高质量发展工作专班的通知.json' (ID: a31b5c9a0dd4c2bdb87f73aba63b9e8f) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/关于公布2024年广东省重点农业龙头企业名单的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/关于公布2024年广东省重点农业龙头企业名单的通知.pdf
跳过更新文件 '关于公布2024年广东省重点农业龙头企业名单的通知.json' (ID: 1326bf0289ce4cf2c095a0825aad6885) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/如何打造低空经济“广东样本”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/如何打造低空经济“广东样本”.pdf
跳过更新文件 '如何打造低空经济“广东样本”.json' (ID: 39aace4624e088030b99e8181ad64df1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.pdf
跳过更新文件 '两个图了解中国海洋渔业行业上，下游产业链及行业相关政策分析「图」.json' (ID: b7103d4c57df422987b53e24d85b5c85) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/洲明科技(300232)答投资者问.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/洲明科技(300232)答投资者问.pdf
跳过更新文件 '洲明科技(300232)答投资者问.json' (ID: 719b0f97823c1fc49f27cda1a2e4439f) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.pdf
跳过更新文件 '粤领低空 智飞未来｜潮涌低空经济：各地重仓哪些应用？广东如何率先“高飞”？.json' (ID: 317ebe42f7c147a06d48424415c69e49) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2024年广东省水产养殖重点品种监测年度报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2024年广东省水产养殖重点品种监测年度报告.pdf
跳过更新文件 '2024年广东省水产养殖重点品种监测年度报告.json' (ID: 2893de5b2b497e6723cc2b4316c2bf97) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.json'。
跳过更新文件 '东证期货_新能源汽车产业链专题报告：基于移动大数据分析汽车行业变迁.pdf.json' (ID: 6c0355350fbf7c6183a2dfa6dc8a209b) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省海洋渔业资源养护补贴政策实施方案》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省海洋渔业资源养护补贴政策实施方案》.pdf
跳过更新文件 '《广东省海洋渔业资源养护补贴政策实施方案》.json' (ID: 7d1efc962647b56c336d2903553ee002) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市推动低空经济高质量发展若干措施.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市推动低空经济高质量发展若干措施.pdf
跳过更新文件 '广州市推动低空经济高质量发展若干措施.json' (ID: bdd688bbf8493b21cf728c138a646a8c) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/向海图强——海洋经济产业链分析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/向海图强——海洋经济产业链分析.pdf
跳过更新文件 '向海图强——海洋经济产业链分析.json' (ID: c1e1c0ba24e03d0d9233cc8e4026eda3) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.pdf
跳过更新文件 '【中国银河研究】中企出海系列：新能源汽车产业链——乘新能源之势，塑海外产业集群.json' (ID: 9d52cc4cbc749bc36d923f3fd137c3d9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.pdf
跳过更新文件 '广东省自然资源厅 广东省发展和改革委员会联合发布《广东海洋经济发展报告（2024）》.json' (ID: bffbdef8c72de4e717c42e31ed97dde1) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2023年新能源汽车行业研究报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2023年新能源汽车行业研究报告.pdf
跳过更新文件 '2023年新能源汽车行业研究报告.json' (ID: 21054c4af8bb623e6de14aa1d959bbef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.pdf
跳过更新文件 '《国务院关于促进海洋渔业持续健康发展的若干意见》（国发〔2013〕11 号）.json' (ID: cdb489d34fa24284f3f8c4fd47b86d73) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.pdf
跳过更新文件 '广东低空经济发展调研报告重磅发布：规模超千亿居全国前列，广深珠三核联动.json' (ID: 532bfdc4201117ff5a01831f875ad0b8) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：2024全球动力电池装车量TOP10出炉，当升科技中伟股份达成战略合作.pdf.json' (ID: 77ff56869a54ba4cb78c77e09a761209) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.pdf
跳过更新文件 '《惠州市推动低空经济高质量发展行动方案（2024-2026年）》.json' (ID: 8af6a9db22e2a9e9fb077f6b7ad44248) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.pdf
跳过更新文件 '低空经济总特征、发展态势与未来态势——低空经济研究之总览篇.json' (ID: f9209efceca5d5178f6c1c322f9e189f) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/鳗鱼价格飙升背后的养殖困境！.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/鳗鱼价格飙升背后的养殖困境！.pdf
跳过更新文件 '鳗鱼价格飙升背后的养殖困境！.json' (ID: 1ea76bfe9787a18a7977a44823b65db9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/让低空经济“飞”进发展新空间.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/让低空经济“飞”进发展新空间.pdf
跳过更新文件 '让低空经济“飞”进发展新空间.json' (ID: 94eac88bc214bb98ee9124cc097606ed) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋经济发展报告（2024）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋经济发展报告（2024）.pdf
跳过更新文件 '广东海洋经济发展报告（2024）.json' (ID: bcb6837bcd946b2b1e388daf217a05d1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/Measurement and Evolution of High-quality Development Level of Marine Fishery in China.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/Measurement and Evolution of High-quality Development Level of Marine Fishery in China.pdf
跳过更新文件 'Measurement and Evolution of High-quality Development Level of Marine Fishery in China.json' (ID: 612510d200602fafbb0946308a2442ca) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.pdf
跳过更新文件 '你所不知的“广东能”_ 广东十大战略性新兴产业“新质”观.json' (ID: 7d020f25e1b43a65384bf24c7f0f5588) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济广东领飞，国资加速入场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济广东领飞，国资加速入场.pdf
跳过更新文件 '低空经济广东领飞，国资加速入场.json' (ID: 67e8d31965ad31207fb0d8daa11a1eb4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.pdf
跳过更新文件 '五城引领、集群联动，界面智库发布《中国低空经济产业链研究报告（全景篇）》.json' (ID: ed051a29513a47b6333dd641df54b346) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链跟踪点评：1-2月新能源汽车销量延续良好增长态势.pdf.json' (ID: 7c381b5f4822636669066915c82420ac) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.pdf
跳过更新文件 '2023年中国海洋渔业产业链图谱研究分析（附产业链全景图）.json' (ID: 374a8f265c0b01b4a5b2185080706d91) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.json'。
跳过更新文件 '民生证券_可转债周报：低空经济产业链转债有哪些？.pdf.json' (ID: e439bfd8a0cbd00c2fdc123fdd288121) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.pdf
跳过更新文件 '《佛山市推动低空经济高质量发展实施方案（2024—2026）》印发.json' (ID: f08ceaf6f3214910aa41dd7147d4b150) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.json'。
跳过更新文件 '东莞证券_新能源汽车产业链周报：广汽发布全固态电池技术.pdf.json' (ID: b670c6d0fb9e31fe41d186ddb6b5fa51) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.pdf
跳过更新文件 '买最贵的苗和料，卖最便宜的鱼！加州鲈“逼”退一大批养殖户后，如今行情涨到飞起.json' (ID: 7bd17d6c8b0fd142249e8f8c49ea7f98) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2009年度农业部远洋渔业企业资格企业名单.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2009年度农业部远洋渔业企业资格企业名单.pdf
跳过更新文件 '2009年度农业部远洋渔业企业资格企业名单.json' (ID: 0bc603051bc2b04329f0fb377a940843) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/远洋渔业-头豹词条报告系列.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/远洋渔业-头豹词条报告系列.pdf
跳过更新文件 '远洋渔业-头豹词条报告系列.json' (ID: ff367167522abe27c6bf1c0ca52fefca) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：固态电池技术路线聚焦，龙头引领量产进程.pdf.json' (ID: 6cfe1399215e51274e9159043ac1faf4) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.pdf
跳过更新文件 '《财政部 农业农村部关于实施渔业发展支持政策推动渔业高质量发展的通知》（财农〔2021〕41 号）.json' (ID: e606194ee7aac45eb4d1faac1097f77f) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《深圳市罗湖区促进商旅文低空应用的若干措施》.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《深圳市罗湖区促进商旅文低空应用的若干措施》.pdf
跳过更新文件 '《深圳市罗湖区促进商旅文低空应用的若干措施》.json' (ID: 684932dc370723cd07b06baf6d4c3d77) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.json'。
跳过更新文件 '开源证券_北交所科技新产业跟踪第十一期：深度探析低空经济产业链上中下游，北交所和新三板集聚特色公司.pdf.json' (ID: 8a81033abf60045759ef47426907049d) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.pdf
跳过更新文件 '江门市工业和信息化局关于开展新能源电池产业链投融资对接交流会的通知.json' (ID: 7bf59618f1dfa2056b16b838256736b3) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.pdf
跳过更新文件 '新能源合作！易事特与雄林科技共推新材料应用及数字能源创新发展.json' (ID: 048a61afeaef94b4a0189ab29a843036) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【专家观点】推动海洋渔业向信息化智能化转型.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/【专家观点】推动海洋渔业向信息化智能化转型.pdf
跳过更新文件 '【专家观点】推动海洋渔业向信息化智能化转型.json' (ID: 70ee470ab4a4fb02c67e1d085ffd997a) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.json'。
跳过更新文件 '2024-09-23东方证券_新能源汽车产业链行业周报：电池企业加码布局商用车市场，储能迎来订单潮.pdf.json' (ID: 5f3487c900b19ca4df524cbf0f779ac9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.pdf
跳过更新文件 '东土科技参股广汽集团飞行汽车初创公司 为低空经济构建新型产业生态.json' (ID: bbff9d37ad216ca0498a37b05a5864c2) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.pdf
跳过更新文件 '低空经济 广东高飞｜工业无人机翱翔多赛道 黄金内湾振翼起航.json' (ID: 30ea62cec458c81ba4f0d5ff2c78ea01) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业深度报告：充电桩行业高景气，超充升级带来新机遇.pdf.json' (ID: ff2e2fdf7638c79a06ae8c6633bee976) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省公布1183家省重点农业龙头企业名单.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省公布1183家省重点农业龙头企业名单.pdf
跳过更新文件 '广东省公布1183家省重点农业龙头企业名单.json' (ID: 8c40e0cd1bd09b225552430c7bfd3ce1) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/从新材料到低空经济，长三角企业前往广东寻找商机.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/从新材料到低空经济，长三角企业前往广东寻找商机.pdf
跳过更新文件 '从新材料到低空经济，长三角企业前往广东寻找商机.json' (ID: 58d52e0278051d12ffb4b35d4c9ccfbb) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/打造海洋渔业全产业链.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/打造海洋渔业全产业链.pdf
跳过更新文件 '打造海洋渔业全产业链.json' (ID: 4573c7f2ee9305f9eb275fe2a256b340) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济产业链梳理_低空领航东吴计算机王紫敬团队.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济产业链梳理_低空领航东吴计算机王紫敬团队.pdf
跳过更新文件 '低空经济产业链梳理_低空领航东吴计算机王紫敬团队.json' (ID: 9d4fdd2b9d236887f1ee90db957c392d) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济四项得分居全国之首.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济四项得分居全国之首.pdf
跳过更新文件 '广东低空经济四项得分居全国之首.json' (ID: bc2648525894d1f5866625605f0236bd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/掘金拉美蓝海市场，广东新能源产业链加快抱团出海.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/掘金拉美蓝海市场，广东新能源产业链加快抱团出海.pdf
跳过更新文件 '掘金拉美蓝海市场，广东新能源产业链加快抱团出海.json' (ID: 11c73caf29aa013407573997139d5e1e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/涉海企业 .pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/涉海企业 .pdf
跳过更新文件 '涉海企业 .json' (ID: 1e3317566fece6edcab9eaf9cbd63640) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025年广东新能源产业链分析.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2025年广东新能源产业链分析.pdf
跳过更新文件 '2025年广东新能源产业链分析.json' (ID: 71c0425ea0f681edcea5743f8557f64b) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].pdf
跳过更新文件 '广东省现代化产业体系发展报告（2023-2024）. 广东省发展改革委.[2025-02].json' (ID: e6f35d755bee415385df34f7d7dc30f1) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/总投资6亿元！两大电池新能源项目在广东奠基.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/总投资6亿元！两大电池新能源项目在广东奠基.pdf
跳过更新文件 '总投资6亿元！两大电池新能源项目在广东奠基.json' (ID: c7eea7ccd2bd6937e3e725876d9ab228) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济发展条例.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市低空经济发展条例.pdf
跳过更新文件 '广州市低空经济发展条例.json' (ID: 45b98454bd950b23cf5e17c3c0fff44b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/开源北交所研究精选 _ 低空经济产业链北交所全梳理.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/开源北交所研究精选 _ 低空经济产业链北交所全梳理.pdf
跳过更新文件 '开源北交所研究精选 _ 低空经济产业链北交所全梳理.json' (ID: 92741f594aa6ae9b651540f0b09b4abc) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.pdf
跳过更新文件 '《农业农村部办公厅关于进一步加强海洋伏季休渔后期监管工作的通知》（农办渔〔2024〕11 号）.json' (ID: 62395782c6ace9e0ba66c4f6372a808e) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.pdf
跳过更新文件 '2025-04-23国金证券_综合类产业行业专题研究报告：海洋经济联合专题-掘金深蓝，向海图强.json' (ID: 6bebb0be5c598c82422a5a32dc1b27fd) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济迎风直上.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济迎风直上.pdf
跳过更新文件 '广东低空经济迎风直上.json' (ID: 9f5876c1887938ab278627ee51ad3f55) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.pdf
跳过更新文件 '第一强省“摊牌”：新能源要破万亿，“秘密武器”来了！.json' (ID: 9b2523fa9c84a0e3a3db4ffa8313f147) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省经济分析报告 2024年.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2024年.pdf
跳过更新文件 '广东省经济分析报告 2024年.json' (ID: 362d45cdc0fbc1c6e4284591c4540dea) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省“十三五”新能源产业发展规划研究.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/广东省“十三五”新能源产业发展规划研究.pdf
跳过更新文件 '广东省“十三五”新能源产业发展规划研究.json' (ID: 68215f2d4d887abc2603ec523727ce60) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2024年广州低空经济产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2024年广州低空经济产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2024年广州低空经济产业链全景图谱.json' (ID: 681f8f11630f670615745a8ce6cd017c) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.pdf
跳过更新文件 '制造业当家 _ 两个背景 三大动能 广东新能源汽车产业蓄势腾飞.json' (ID: fc45d47721a54f78f1e1f3d222876468) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省经济分析报告 2024年第一季度.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2024年第一季度.pdf
跳过更新文件 '广东省经济分析报告 2024年第一季度.json' (ID: 0a95b6cf64f854a40cb4dfab01bf635a) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济发展实施方案.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市低空经济发展实施方案.pdf
跳过更新文件 '广州市低空经济发展实施方案.json' (ID: 87bba05458bb87d37d73eaf8bcfc3c41) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.pdf
跳过更新文件 '广东省人民政府办公厅关于推动能源科技创新促进能源产业发展的实施意见.json' (ID: 69eef0faa66c2addd18f6dbb082b8d04) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/汕尾市海洋养殖发展规划.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/汕尾市海洋养殖发展规划.pdf
跳过更新文件 '汕尾市海洋养殖发展规划.json' (ID: c7ca721ab1433db81edaa8ca4a6a9a33) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.pdf
跳过更新文件 '广州市花都区人民政府办公室关于印发花都区支持新能源产业高质量发展的十条措施的通知.json' (ID: 462618b1e123b047202144213ec94d27) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.json'。
跳过更新文件 '万联证券_交通运输行业深度报告：低空经济产业链梳理之总览篇.pdf.json' (ID: 89ca9f96739060d31250599ece179916) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/中国船级社支持业界打造海洋渔业装备全产业链.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/中国船级社支持业界打造海洋渔业装备全产业链.pdf
跳过更新文件 '中国船级社支持业界打造海洋渔业装备全产业链.json' (ID: b8a342372d15c7a42e892c01e372da98) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.json'。
跳过更新文件 '东方证券_新能源汽车产业链行业周报：特斯拉上海储能工厂将投产，亿纬锂能签储能大单.pdf.json' (ID: bc7cbd79e3359758720ace7173af9037) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.pdf
跳过更新文件 '集聚全国超三成低空企业，广东低空经济航母群如何“链”成？｜未来产业看广东.json' (ID: f3cbcdcc93cb7cc445fb98efbe290ade) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2019-2025年广东省能源市场运行态势及行业发展前景预测报告.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2019-2025年广东省能源市场运行态势及行业发展前景预测报告.pdf
跳过更新文件 '2019-2025年广东省能源市场运行态势及行业发展前景预测报告.json' (ID: b85e58fb792e3dd3c2698524eb7e748c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.pdf
跳过更新文件 '预见2025：《2025年中国新能源汽车行业全景图谱》（附市场现状、竞争格局和发展趋势等）.json' (ID: 7984b91c0c98539d672e76eb509957ef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.pdf
跳过更新文件 '广东海洋生产总值率先突破2万亿元 连续30年居全国首位——广东2025年世界海洋日暨全国海洋宣传日主场活动在深圳举办.json' (ID: fd9990215fcb32a1e9fb9f8da5b1ec29) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.pdf
跳过更新文件 '拥抱“新蓝海”，拓展新空间广东着力培育万亿级现代化海洋牧场产业集群.json' (ID: 5252f4afc4015b09ceaa492b8ea97d53) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州新政助推智能网联新能源汽车产业发展.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州新政助推智能网联新能源汽车产业发展.pdf
跳过更新文件 '广州新政助推智能网联新能源汽车产业发展.json' (ID: 975abcf115ffea436f927090b7eddfdf) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/浙江海洋渔业产业链及其贡献度分析.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/浙江海洋渔业产业链及其贡献度分析.pdf
跳过更新文件 '浙江海洋渔业产业链及其贡献度分析.json' (ID: 2debf5fc2c06d38b0faf9206e9f96667) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州新能源汽车产业链升级 项目从动工到试投产仅10个月.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州新能源汽车产业链升级 项目从动工到试投产仅10个月.pdf
跳过更新文件 '广州新能源汽车产业链升级 项目从动工到试投产仅10个月.json' (ID: e95d1dc942f4acb089425c376d4c6f10) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2022年渔业行业研究报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/2022年渔业行业研究报告.pdf
跳过更新文件 '2022年渔业行业研究报告.json' (ID: 365d4255501e6123dad64408c6137f54) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.pdf
跳过更新文件 '粤省情发布《广东天际新机遇2024：广东低空经济发展调查研究报告》.json' (ID: 6548768fbe70b589d3fbaa558e54462c) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.json'。
跳过更新文件 '万联证券_通信行业周观点：聚焦低空经济产业链上下游的技术创新.pdf.json' (ID: dda2f4358d3d541e52f57ea4efd67064) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省推动低空经济高质量发展行动方案（2024—2026年）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省推动低空经济高质量发展行动方案（2024—2026年）.pdf
跳过更新文件 '广东省推动低空经济高质量发展行动方案（2024—2026年）.json' (ID: 17bcfb047442d923006e74f7d2c7c337) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/产业科技创融看广东丨低空经济链上企业超三成在广东.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/产业科技创融看广东丨低空经济链上企业超三成在广东.pdf
跳过更新文件 '产业科技创融看广东丨低空经济链上企业超三成在广东.json' (ID: 70d1fa543cd655d9bdfe3c219798b496) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/【产业图谱】2025年广东低空经济产业链全景图谱.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/【产业图谱】2025年广东低空经济产业链全景图谱.pdf
跳过更新文件 '【产业图谱】2025年广东低空经济产业链全景图谱.json' (ID: fc4202d92f55be2951feba09a1582ec3) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.json'。
跳过更新文件 '湛江经济技术开发区渔港经济区建设规划（2024-2035 年）修编.json' (ID: 1b91c8eda0e129ba8c9add5980249000) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.pdf
跳过更新文件 '广东省工业和信息化厅 广东省发展和改革委员会广东省科学技术厅 广东省商务厅 广东省市场监督管理局关于印发广东省发展汽车战略性支柱产业集群行动计划（2023—2025年）的通知.json' (ID: d342aff474136bc2e86cf69df4fffbe2) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.pdf
跳过更新文件 '广州市人民政府办公厅关于印发广州市海洋经济发展“十四五”规划的通知.json' (ID: 08ae1a43953d69e3f884f802c9a9ffeb) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.pdf
跳过更新文件 '莱尔科技：一季度营收同比增长65.94%新能源材料业务成增长主力.json' (ID: 4b30e3bec0b24cc559b2a7927d2974fd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省渔业企业列表.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省渔业企业列表.pdf
跳过更新文件 '广东省渔业企业列表.json' (ID: 2eb9a15357f96dd4b43c24947e917b6e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济：来自南方的创新力量.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济：来自南方的创新力量.pdf
跳过更新文件 '广东低空经济：来自南方的创新力量.json' (ID: 4be4d8f43303114bd74e22cc4c7bed29) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/zf.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: 'zf.json'。
跳过更新文件 'zf.json' (ID: 819bdbde26bea3dab1d223bf07d85fef) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/逆全球化背景下的电车出口跟踪.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '逆全球化背景下的电车出口跟踪.json'。
跳过更新文件 '逆全球化背景下的电车出口跟踪.json' (ID: 2e71445e39c51159ae415532c1f380c3) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.pdf
跳过更新文件 '广东低空经济蓄势腾飞！粤省情发布《广东低空经济发展调研报告》.json' (ID: ffedb4dedecc33fa6f742f60664c6e83) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.pdf
跳过更新文件 '中山市人民政府办公室关于印发中山市进一步推动新能源产业做大做强的若干政策措施的通知.json' (ID: 5f13067f68922f8656f1bca1f3d4fed7) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东阳江海洋渔业经济得到长足发展.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东阳江海洋渔业经济得到长足发展.pdf
跳过更新文件 '广东阳江海洋渔业经济得到长足发展.json' (ID: abfeaf07b1257e4f3333d97368962974) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东低空经济发展调研报告发布，深穗企业数位列全国前二.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东低空经济发展调研报告发布，深穗企业数位列全国前二.pdf
跳过更新文件 '广东低空经济发展调研报告发布，深穗企业数位列全国前二.json' (ID: b8bfbf7dc8eb358db3526962ff26ae92) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/新能源汽车专业人才需求调研报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/新能源汽车专业人才需求调研报告.pdf
跳过更新文件 '新能源汽车专业人才需求调研报告.json' (ID: 02be550cc383b64d92acc9f258e83745) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
错误: 本地 PDF 文件 'downloaded_pdfs/.pdf' 不存在，无法上传/更新到 MinIO。来自 JSON: '.json'。
跳过更新文件 '.json' (ID: 9d52cc4cbc749bc36d923f3fd137c3d9) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省促进海洋经济高质量发展条例.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/广东省促进海洋经济高质量发展条例.pdf
跳过更新文件 '广东省促进海洋经济高质量发展条例.json' (ID: d8a178316fc530a456af8dc6c652beba) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/变与不变——新能源汽车行业研究框架.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/变与不变——新能源汽车行业研究框架.pdf
跳过更新文件 '变与不变——新能源汽车行业研究框架.json' (ID: 26bc1e8911417b019a619281e227ab29) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.pdf
跳过更新文件 '政策解读 _ 广东省培育新能源战略性新兴产业集群行动计划（2021－2025年）.json' (ID: 638b242a64490f3ecbd6ca07227a7e5b) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/海洋经济（二）——海洋渔业产业链全景.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/海洋经济（二）——海洋渔业产业链全景.pdf
跳过更新文件 '海洋经济（二）——海洋渔业产业链全景.json' (ID: a73cbe0fdbeb9cde9441cc885f8277a4) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/珠海国资国企探索低空经济“珠海模式”.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/珠海国资国企探索低空经济“珠海模式”.pdf
跳过更新文件 '珠海国资国企探索低空经济“珠海模式”.json' (ID: 8d685bcee4d5748fe64a2ad33e1ecdc5) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/低空经济发展研究报告（2024）.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/低空经济发展研究报告（2024）.pdf
跳过更新文件 '低空经济发展研究报告（2024）.json' (ID: 5c248585721aeea922671d262907cd3e) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广州市低空经济综合实力与发展潜力全面评估报告.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广州市低空经济综合实力与发展潜力全面评估报告.pdf
跳过更新文件 '广州市低空经济综合实力与发展潜力全面评估报告.json' (ID: db9f237b45b08717216e03d3f25c9584) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.pdf
跳过更新文件 '前七月全省水产品总产量稳中有增，产量超四百九十万吨 广东加快建设全链条现代化海洋牧场.json' (ID: b719738b009b5602f21b5e30a0de5afd) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/湛江市现代化海洋牧场建设规划 （2023-2035年）.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/湛江市现代化海洋牧场建设规划 （2023-2035年）.pdf
跳过更新文件 '湛江市现代化海洋牧场建设规划 （2023-2035年）.json' (ID: e5d03b7570df3a4797dc149e7ee79a75) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.pdf
跳过更新文件 '《广东省培育新能源战略性新兴产业集群行动计划(2021-2025年)》政策解读.json' (ID: 2fa45a6563e71149cc41e5f5796529d4) 到 MongoDB 集合 'policy_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/2021年中国新能源汽车换电市场－艾瑞.pdf' 到 MinIO: tiance-industry-finance/research_report/2025/7/3/2021年中国新能源汽车换电市场－艾瑞.pdf
跳过更新文件 '2021年中国新能源汽车换电市场－艾瑞.json' (ID: cf17b8255096b5ffb6c57ac06dca106e) 到 MongoDB 集合 'research_report_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/广东省经济分析报告 2025年第一季度.pdf' 到 MinIO: tiance-industry-finance/news/2025/7/3/广东省经济分析报告 2025年第一季度.pdf
跳过更新文件 '广东省经济分析报告 2025年第一季度.json' (ID: 006d1d0cdbc8ce5bbe43677bd7a0ce90) 到 MongoDB 集合 'news_label_info_new'。
跳过 MinIO 存储桶 'tiance-industry-finance' 的创建，假设其已存在。
True
已跳过文件(文件已存在) 'downloaded_pdfs/习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .pdf' 到 MinIO: tiance-industry-finance/policy/2025/7/3/习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .pdf
跳过更新文件 '习近平主持召开中央财经委员会第六次会议强调：纵深推进全国统一大市场建设 推动海洋经济高质量发展 .json' (ID: 01ef1fcd6f46170c738d7fedcc198eb4) 到 MongoDB 集合 'policy_label_info_new'。

处理完成。所有错误和警告信息已记录到 'error_logs/processing_errors_20250707_142547.txt'。
