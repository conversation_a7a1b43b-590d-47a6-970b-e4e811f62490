<!DOCTYPE html>





<html>

<head>
	<meta charset="utf-8" />
	<title>
		统一身份认证平台
	</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="stylesheet" href="assets/plugins/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" href="assets/css/style.css?v=0.37968432579181677">
	<link rel="stylesheet" href="assets/fonts/font-awesome-4.4.0/css/font-awesome.css" />
	<link rel="stylesheet" href="assets/text-security/text-security.css" />
	<link rel="stylesheet" href="assets/text-security/text-security-login.css" />
	<link rel="stylesheet" href="css/tac.css">

	<script type='text/javascript' src='js/qrcode/qrcode.js'></script>
	<script type='text/javascript' src="js/jquery/jquery-1.11.1-min.js"></script>
	<script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.9.js"></script>
	<script type="text/javascript" src="js/layer/layer.js"></script>
	<script type="text/javascript" src="js/i18n.js"></script>
	<script type='text/javascript' src="js/login/security.js?v=0.12560494395210653"></script>
	<script type="text/javascript" src="js/login/pwdYz.js?v=0.4940793659182138"></script>
	<script type="text/javascript" src="js/login/login.js?v=0.3683774935595058"></script>
	<script src="js/login/tac.min.js"></script>
	
<style>
		.main {
	    		display: flex;
    			align-items: center;
    			padding: 30px 0;
		}
		.main .login-container {
	    		position: relative;
        		min-height: 340px;
        		height: auto;
        		margin: 0 0 0 30px;
		}
		.login-form {
			padding-bottom: 0;
		}
		.cjgzs-class {
	    		margin-top: 35px;
	        	position: relative;
    			bottom: 0;
		}
		.login-info .error {
			position: relative;
			top: 0;
		}
		@media (max-width: 768px) {
			.main {
				justify-content: center;
	        		padding: 0;
			}
			.login-container {
				margin: 0!important;
				flex: 1;
			}
			.login-box {
				min-height: auto !important;
			}
			.code-img {
				width: 33%;
			}
		}
	</style>
	
</head>

<body id="login_new">
<!DOCTYPE html>

<script type="text/javascript">
    //判断操作系统和浏览器类型，需要判断的页面加上detectOS()；
    function detectOS() {
        var sUserAgent = navigator.userAgent;
        var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
        if (isWin) {
            if(navigator.userAgent.indexOf("MSIE 6.0")>0
                || navigator.userAgent.indexOf("MSIE 7.0")>0
                || navigator.userAgent.indexOf("MSIE 8.0")>0){ //IE6、7、8、9
                window.location.href = "https://cas.hnu.edu.cn/system/browser.zf";
            }
        }
    }
    detectOS();
</script>
<html>
<head>
    <meta charset="utf-8" />
</head>
<body></body>
</html>
<!--<input id="send_error" type="hidden" value="loginView.sendsms.error "/>
-->









<div class="login-page">
	<div class="top_logo">
				<img src="assets/images/logo.png" class="">
			</div>
	<!--<div style="padding:10px 20px;text-align:right;font-size:14px">
				<a href="login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex&locale=zh_CN" style="color:#fff;">中文 | </a>
				<a href="login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex&locale=en" style="color:#fff;">English </a>open
			</div>-->
	<div class="main">
	<div class="login-container">
		<div class="login-box col-xs-12">
			<div class="login-header" style="display:none;">
				<div>
					<a href="#qcode">
						<img class="hidden-xs" src="assets/images/qcode-new.png">
						<img class="hidden-lg hidden-md hidden-sm" src="assets/images/qcode-new.png">
					</a>
				</div>
				<div style="display:none;">
					<a href="#qcodcasepc"><img src="assets/images/qcode-pc.png"></a>
				</div>
				<input type="hidden" id="uuid" value="" />
				<input type="hidden" id="jrhxQrcode" />
				<input type="hidden" id="jrhxImage" />
				<input type="hidden" id="transId" />
				<input type="hidden" id="baseUrl" value="http://cas.hnu.edu.cn:80/cas"/>
			</div>
			<div class="col-xs-12 login-form-wrap" >
				<div class="login-form" id="qcode">
					<div class="tabs">
						<div class="clickin" id="zhdl">账号登录</div>
						<!--<div id="sjdl" >手机号登录</div>-->
					</div>
					<!-- 图片验证码 -->

					<div class="tab-content">
						
							<div id="captcha-box" style="display:none;
											position: absolute;
										    left: 50%;
										    top: 50%;
										    align-items: center;
										    justify-content: center;
										    transform: translate(-50%, -50%);
										    z-index: 99;" ></div>
							<input type="hidden" id="casCaptValue" value="">
							<input type="hidden" id="tcYzmSfqd" value="1" />
						
						<div class="tab-pane" id="zhdl_pane" style="display:block">
							<form id="fm1" action="login?v=0.43023102582935524 " method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/user.png" ></div>
										<input id="username" name="username" class="form-control user-input" tabindex="1" placeholder="职工号/学号" type="text" value="S2410W1114" size="25" autocomplete="off"/>
									</div>
								<!--	<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group">
									<div class="input-group col-lg-9 col-md-9 col-sm-9 col-xs-9">
										<div class="input-group-addon" style="width:25px;left:9px;"><img src="assets/images/lock.png" ></div>
										<input class="form-control pwd-input my-password-field" id="ppassword" placeholder="密码"
											   tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="password" value="" size="25" autocomplete="off">
										<input hidden id="password" name="password" placeholder="密码"  value="" tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="text" value="" size="25" autocomplete="off">

											
									</div>
									<a class="forget-pwd txt" target="_blank" href="https://cas.hnu.edu.cn/V3/securitycenter/findPwd/index.zf" >忘记密码</a>
										
								</div>
								<div class="code" id="kaptcha">
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
										<div class="input-group col-xs-8">
											<div class="input-group-addon icon-verfiy" ><img src="assets/images/icon-verfiy.png" ></div>
											<input id="authcode" name="authcode" class="form-control" tabindex="2" placeholder="验证码" type="text" value="" size="10" autocomplete="off"/>
										</div>
										<div class="col-xs-4 p-0 code-img"><img id="yzmPic" onclick="javascript:refreshCode();" class="" />
											<a href="javascript:refreshCode();">
												看不清 ? </a>
										</div>
									</div>
								</div>
								<div class="login-info">
									<p class="error text-left" id="errormsg">
										<span id="msg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="remember-me ">
										<input class="default-checkbox checkbox" type="checkbox" name="rememberMe" value="true"  id="rember">
										<!--<label for="rember">记住我 </label>-->
										<label for="rember">记住账号密码</label>
									</div>
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0 login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
						<div class="tab-pane" id="sjdl_pane" style="display:none;">
							<form id="fm2" action="/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex" method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-smsmsgxs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/icon-use.png" ></div>
										<input id="phone" name="username" class="form-control user-input" tabindex="1" placeholder="输入手机号码" type="text" value="S2410W1114" size="25" autocomplete="off"/>

									</div>
									<!--<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group" id="phoneCard">
									<div class="input-group col-lg-7 col-md-7 col-sm-7 col-xs-7">
										
										<div class="input-group-addon icon-verfiy"><img src="assets/images/icon-verfiy.png" ></div>
										<input id="mobileCode" name="mobileCode" class="form-control pwd-input" tabindex="2" placeholder="输入短信验证码" type="password" value="" size="25" autocomplete="off"/>
										<input id="password" name="password" class="form-control pwd-input" tabindex="2" style="display: none;" type="password" value="" size="25" autocomplete="off"/>
									</div>

									<input type="button" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 sendBtn" value="获取短信验证码" id="sendsms" />
								</div>
								<div id="sumbit">
									<p class="error text-left"id="smsErrorMsg">
										<span id="smsmsg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl2">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class" style="display:none;">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType2" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType2">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
					</div>
					<div class="login-footer">
						<div class="text-center" style="display:none;">
							<!--<p class="text-left notice">
										<a href="#" class="login-info-text hidden-lg hidden-md">登录须知 </a>
									</p>-->
							
							<p>第三方账号登录</p>
							<div class="login-way">
								<a id = "wxFrame" href="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient&response_type=code&scope=snsapi_login#wechat_redirect" ><img src="assets/images/icon-weixin.png" /></a>
								<a  id = "qqFrame" href="https://graph.qq.com/oauth2.0/authorize?client_id=434242&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DQQClient&response_type=code&state=test"><img src="assets/images/icon-QQ.png" /></a>
								<a id = "ddFrame" href="https://oapi.dingtalk.com/connect/qrconnect?appid=xx&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DDingDingClient"><img src="assets/images/icon-dingding.png" /></a>
								<!--<a href="TxWxLogin_pc.html" id="txwx"><i class="fa fa-weixin"></i></a>
										<a href="https://cas.hnu.edu.cn/cas/SbkLoginUrl.html?client_name=SbkClient"><i class="fa fa-qq"></i></a>-->
								<!--<a href="javascript:void(0);"><i class="fa fa-qq"></i></a>
                                <a href="javascript:void(0);"><i class="fa fa-weixin"></i></a>-->
							</div>
						</div>
					</div>

				</div>
				<div class="qcode" id="qcodepc" style="display:none">
					<p>统一身份认证</p>
					<div id="qrcode">
						<div id="qrcode-img">
							<img id="qrcodeImg" alt="Scan me!" style="display: none;">
						</div>
					</div>
					<div id="qrcode-content">
						<div class="qrcode-bg"></div>
						<p class="refresh-text">

							<a href="#" id="qrcode_refresh"><i class="fa fa-refresh"></i>刷新 </a>
						</p>
					</div>
					<!--		扫码成功提示模态框			-->
					<div id="qrcode-success" style="width: 190px;height: 190px;position: absolute;top: 62px;left: 50%;margin-left: -95px;background: rgba(255, 255, 255, 0.9);text-align: center;display: none;">
						<div style="width: 106px; height: 106px; margin: 0 auto; margin-top: 28px">
							<img id="qrcodeSuccess" src="images/smsuccess.png" style="width: 106px; font-size: 0;">
						</div>

						<span class="success-text" style="display: inline-block; color: #000; font-size: 18px; font-weight: 700">扫码成功</span>
					</div>
					<span>打开正方云app<br />在【首页】页面左上角打开扫一扫</span>
				</div>
			</div>
		</div>
	</div>
	

<div class="rwm" style="padding-top: 90px;width: 350px;float: right;height: 400px;overflow: hidden;">
	<h4 style="text-align: center;">微信扫码登录</h4>
	<div id="wxLogin"></div>
</div>
<!--
<div class="rwm" style="padding-top:90px;"><iframe id="wxFrame" src="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=http%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient%26service%3Dhttps%3A%2F%2Fpt.hnu.edu.cn%2F&response_type=code&scope=snsapi_login#wechat_redirect
				"></iframe></div>
  -->
</div>
	<div class="footer">
               <p>湖南大学版权所有©2020年    通讯地址:湖南省长沙市岳麓区麓山南路麓山门    邮编：410082   </p>
               <p>湖南大学 湘ICP备09007699号 湘教QS3-200503-000481 湘教QS4-201312-010059 技术服务电话：88821520</p>
            </div>
</div>
<script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
<script>

	$(function () {
		    var url = "http://cas.hnu.edu.cn/cas/login?client_name=WeiXinClient";
		    var service = GetQueryString("service");
		    console.log('service: ', service);
		    if (service) {
				  url = url + '&service='+service;
			}
			var wxObj = new WxLogin({
				self_redirect: false,
				id: "wxLogin", 
				appid: "wx6bec8ae941e46eef", 
				scope: "snsapi_login", 
				redirect_uri: url,
				stylelite: 1
			});

		
		/*
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#wxFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=WeiXinClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#wxFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('wxFrame').href=$('#wxFrame').attr('href');
			},100);
		}
		*/
	})
	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#qqFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=QQClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#qqFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('qqFrame').href=$('#qqFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#ddFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("client_name");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href );
			var srcStr = pre + "&redirect_uri=" + ser + "%26" + endStr;
			$('#ddFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('ddFrame').href=$('#ddFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if(service!=null){
			$('#txwx').attr('href','TxWxLogin_pc.html?service='+service);
		}
	});
	function GetQueryString(name) {
		var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if(r!=null){
			return  unescape(r[2]);
		}else{
			return null;
		}
	}
</script>
</body>

</html>