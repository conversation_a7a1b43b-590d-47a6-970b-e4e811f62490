<!DOCTYPE html>





<html>

<head>
	<meta charset="utf-8" />
	<title>
		统一身份认证平台
	</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="stylesheet" href="assets/plugins/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" href="assets/css/style.css?v=0.1972128407395909">
	<link rel="stylesheet" href="assets/fonts/font-awesome-4.4.0/css/font-awesome.css" />
	<link rel="stylesheet" href="assets/text-security/text-security.css" />
	<link rel="stylesheet" href="assets/text-security/text-security-login.css" />
	<link rel="stylesheet" href="css/tac.css">

	<script type='text/javascript' src='js/qrcode/qrcode.js'></script>
	<script type='text/javascript' src="js/jquery/jquery-1.11.1-min.js"></script>
	<script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.9.js"></script>
	<script type="text/javascript" src="js/layer/layer.js"></script>
	<script type="text/javascript" src="js/i18n.js"></script>
	<script type='text/javascript' src="js/login/security.js?v=0.8004024301065282"></script>
	<script type="text/javascript" src="js/login/pwdYz.js?v=0.4679854370603985"></script>
	<script type="text/javascript" src="js/login/login.js?v=0.7373236637653127"></script>
	<script src="js/login/tac.min.js"></script>
	
<style>
		.main {
	    		display: flex;
    			align-items: center;
    			padding: 30px 0;
		}
		.main .login-container {
	    		position: relative;
        		min-height: 340px;
        		height: auto;
        		margin: 0 0 0 30px;
		}
		.login-form {
			padding-bottom: 0;
		}
		.cjgzs-class {
	    		margin-top: 35px;
	        	position: relative;
    			bottom: 0;
		}
		.login-info .error {
			position: relative;
			top: 0;
		}
		@media (max-width: 768px) {
			.main {
				justify-content: center;
	        		padding: 0;
			}
			.login-container {
				margin: 0!important;
				flex: 1;
			}
			.login-box {
				min-height: auto !important;
			}
			.code-img {
				width: 33%;
			}
		}
	</style>
	
</head>

<body id="login_new">
<!DOCTYPE html>

<script type="text/javascript">
    //判断操作系统和浏览器类型，需要判断的页面加上detectOS()；
    function detectOS() {
        var sUserAgent = navigator.userAgent;
        var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
        if (isWin) {
            if(navigator.userAgent.indexOf("MSIE 6.0")>0
                || navigator.userAgent.indexOf("MSIE 7.0")>0
                || navigator.userAgent.indexOf("MSIE 8.0")>0){ //IE6、7、8、9
                window.location.href = "https://cas.hnu.edu.cn/system/browser.zf";
            }
        }
    }
    detectOS();
</script>
<html>
<head>
    <meta charset="utf-8" />
</head>
<body></body>
</html>
<!--<input id="send_error" type="hidden" value="loginView.sendsms.error "/>
-->









<div class="login-page">
	<div class="top_logo">
				<img src="assets/images/logo.png" class="">
			</div>
	<!--<div style="padding:10px 20px;text-align:right;font-size:14px">
				<a href="login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex&locale=zh_CN" style="color:#fff;">中文 | </a>
				<a href="login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex&locale=en" style="color:#fff;">English </a>open
			</div>-->
	<div class="main">
	<div class="login-container">
		<div class="login-box col-xs-12">
			<div class="login-header" style="display:none;">
				<div>
					<a href="#qcode">
						<img class="hidden-xs" src="assets/images/qcode-new.png">
						<img class="hidden-lg hidden-md hidden-sm" src="assets/images/qcode-new.png">
					</a>
				</div>
				<div style="display:none;">
					<a href="#qcodcasepc"><img src="assets/images/qcode-pc.png"></a>
				</div>
				<input type="hidden" id="uuid" value="7875721349410001460" />
				<input type="hidden" id="jrhxQrcode" />
				<input type="hidden" id="jrhxImage" />
				<input type="hidden" id="transId" />
				<input type="hidden" id="baseUrl" value="http://cas.hnu.edu.cn:80/cas"/>
			</div>
			<div class="col-xs-12 login-form-wrap" >
				<div class="login-form" id="qcode">
					<div class="tabs">
						<div class="clickin" id="zhdl">账号登录</div>
						<!--<div id="sjdl" >手机号登录</div>-->
					</div>
					<!-- 图片验证码 -->

					<div class="tab-content">
						
							<div id="captcha-box" style="display:none;
											position: absolute;
										    left: 50%;
										    top: 50%;
										    align-items: center;
										    justify-content: center;
										    transform: translate(-50%, -50%);
										    z-index: 99;" ></div>
							<input type="hidden" id="casCaptValue" value="">
							<input type="hidden" id="tcYzmSfqd" value="1" />
						
						<div class="tab-pane" id="zhdl_pane" style="display:block">
							<form id="fm1" action="login?v=0.08472430205044745 " method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/user.png" ></div>
										<input id="username" name="username" class="form-control user-input" tabindex="1" placeholder="职工号/学号" type="text" value="" size="25" autocomplete="off"/>
									</div>
								<!--	<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group">
									<div class="input-group col-lg-9 col-md-9 col-sm-9 col-xs-9">
										<div class="input-group-addon" style="width:25px;left:9px;"><img src="assets/images/lock.png" ></div>
										<input class="form-control pwd-input my-password-field" id="ppassword" placeholder="密码"
											   tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="password" value="" size="25" autocomplete="off">
										<input hidden id="password" name="password" placeholder="密码"  value="" tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="text" value="" size="25" autocomplete="off">

											
									</div>
									<a class="forget-pwd txt" target="_blank" href="https://cas.hnu.edu.cn/V3/securitycenter/findPwd/index.zf" >忘记密码</a>
										
								</div>
								<div class="code" id="kaptcha">
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
										<div class="input-group col-xs-8">
											<div class="input-group-addon icon-verfiy" ><img src="assets/images/icon-verfiy.png" ></div>
											<input id="authcode" name="authcode" class="form-control" tabindex="2" placeholder="验证码" type="text" value="" size="10" autocomplete="off"/>
										</div>
										<div class="col-xs-4 p-0 code-img"><img id="yzmPic" onclick="javascript:refreshCode();" class="" />
											<a href="javascript:refreshCode();">
												看不清 ? </a>
										</div>
									</div>
								</div>
								<div class="login-info">
									<p class="error text-left" id="errormsg">
										
									</p>
									<div class="remember-me ">
										<input class="default-checkbox checkbox" type="checkbox" name="rememberMe" value="true"  id="rember">
										<!--<label for="rember">记住我 </label>-->
										<label for="rember">记住账号密码</label>
									</div>
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0 login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
						<div class="tab-pane" id="sjdl_pane" style="display:none;">
							<form id="fm2" action="/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex" method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-smsmsgxs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/icon-use.png" ></div>
										<input id="phone" name="username" class="form-control user-input" tabindex="1" placeholder="输入手机号码" type="text" value="" size="25" autocomplete="off"/>

									</div>
									<!--<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group" id="phoneCard">
									<div class="input-group col-lg-7 col-md-7 col-sm-7 col-xs-7">
										
										<div class="input-group-addon icon-verfiy"><img src="assets/images/icon-verfiy.png" ></div>
										<input id="mobileCode" name="mobileCode" class="form-control pwd-input" tabindex="2" placeholder="输入短信验证码" type="password" value="" size="25" autocomplete="off"/>
										<input id="password" name="password" class="form-control pwd-input" tabindex="2" style="display: none;" type="password" value="" size="25" autocomplete="off"/>
									</div>

									<input type="button" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 sendBtn" value="获取短信验证码" id="sendsms" />
								</div>
								<div id="sumbit">
									<p class="error text-left"id="smsErrorMsg">
										
									</p>
									<div class="login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl2">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class" style="display:none;">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType2" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType2">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
					</div>
					<div class="login-footer">
						<div class="text-center" style="display:none;">
							<!--<p class="text-left notice">
										<a href="#" class="login-info-text hidden-lg hidden-md">登录须知 </a>
									</p>-->
							
							<p>第三方账号登录</p>
							<div class="login-way">
								<a id = "wxFrame" href="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient&response_type=code&scope=snsapi_login#wechat_redirect" ><img src="assets/images/icon-weixin.png" /></a>
								<a  id = "qqFrame" href="https://graph.qq.com/oauth2.0/authorize?client_id=434242&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DQQClient&response_type=code&state=test"><img src="assets/images/icon-QQ.png" /></a>
								<a id = "ddFrame" href="https://oapi.dingtalk.com/connect/qrconnect?appid=xx&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DDingDingClient"><img src="assets/images/icon-dingding.png" /></a>
								<!--<a href="TxWxLogin_pc.html" id="txwx"><i class="fa fa-weixin"></i></a>
										<a href="https://cas.hnu.edu.cn/cas/SbkLoginUrl.html?client_name=SbkClient"><i class="fa fa-qq"></i></a>-->
								<!--<a href="javascript:void(0);"><i class="fa fa-qq"></i></a>
                                <a href="javascript:void(0);"><i class="fa fa-weixin"></i></a>-->
							</div>
						</div>
					</div>

				</div>
				<div class="qcode" id="qcodepc" style="display:none">
					<p>统一身份认证</p>
					<div id="qrcode">
						<div id="qrcode-img">
							<img id="qrcodeImg" alt="Scan me!" style="display: none;">
						</div>
					</div>
					<div id="qrcode-content">
						<div class="qrcode-bg"></div>
						<p class="refresh-text">

							<a href="#" id="qrcode_refresh"><i class="fa fa-refresh"></i>刷新 </a>
						</p>
					</div>
					<!--		扫码成功提示模态框			-->
					<div id="qrcode-success" style="width: 190px;height: 190px;position: absolute;top: 62px;left: 50%;margin-left: -95px;background: rgba(255, 255, 255, 0.9);text-align: center;display: none;">
						<div style="width: 106px; height: 106px; margin: 0 auto; margin-top: 28px">
							<img id="qrcodeSuccess" src="images/smsuccess.png" style="width: 106px; font-size: 0;">
						</div>

						<span class="success-text" style="display: inline-block; color: #000; font-size: 18px; font-weight: 700">扫码成功</span>
					</div>
					<span>打开正方云app<br />在【首页】页面左上角打开扫一扫</span>
				</div>
			</div>
		</div>
	</div>
	

<div class="rwm" style="padding-top: 90px;width: 350px;float: right;height: 400px;overflow: hidden;">
	<h4 style="text-align: center;">微信扫码登录</h4>
	<div id="wxLogin"></div>
</div>
<!--
<div class="rwm" style="padding-top:90px;"><iframe id="wxFrame" src="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=http%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient%26service%3Dhttps%3A%2F%2Fpt.hnu.edu.cn%2F&response_type=code&scope=snsapi_login#wechat_redirect
				"></iframe></div>
  -->
</div>
	<div class="footer">
               <p>湖南大学版权所有©2020年    通讯地址:湖南省长沙市岳麓区麓山南路麓山门    邮编：410082   </p>
               <p>湖南大学 湘ICP备09007699号 湘教QS3-200503-000481 湘教QS4-201312-010059 技术服务电话：88821520</p>
            </div>
</div>
<script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
<script>

	$(function () {
		    var url = "http://cas.hnu.edu.cn/cas/login?client_name=WeiXinClient";
		    var service = GetQueryString("service");
		    console.log('service: ', service);
		    if (service) {
				  url = url + '&service='+service;
			}
			var wxObj = new WxLogin({
				self_redirect: false,
				id: "wxLogin", 
				appid: "wx6bec8ae941e46eef", 
				scope: "snsapi_login", 
				redirect_uri: url,
				stylelite: 1
			});

		
		/*
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#wxFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=WeiXinClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#wxFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('wxFrame').href=$('#wxFrame').attr('href');
			},100);
		}
		*/
	})
	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#qqFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=QQClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#qqFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('qqFrame').href=$('#qqFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#ddFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("client_name");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href );
			var srcStr = pre + "&redirect_uri=" + ser + "%26" + endStr;
			$('#ddFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('ddFrame').href=$('#ddFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if(service!=null){
			$('#txwx').attr('href','TxWxLogin_pc.html?service='+service);
		}
	});
	function GetQueryString(name) {
		var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if(r!=null){
			return  unescape(r[2]);
		}else{
			return null;
		}
	}
</script>
</body>

</html>