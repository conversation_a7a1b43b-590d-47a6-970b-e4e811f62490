#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整重定向流程测试 - 跟踪CAS登录的完整重定向链
"""

import requests
import re
import os
import time
from urllib.parse import urlparse, parse_qs

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def test_full_cas_flow():
    """测试完整的CAS登录流程"""
    print("=== 完整CAS登录流程测试 ===")
    
    session = requests.Session()
    
    # 禁用代理
    session.proxies = {
        'http': None,
        'https': None
    }
    
    # 设置通用headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1"
    })
    
    try:
        # 步骤1：获取登录页面
        print("\n1. 获取登录页面...")
        response = session.get(login_url, timeout=15)
        response.raise_for_status()
        
        print(f"状态码: {response.status_code}")
        print(f"URL: {response.url}")
        
        # 解析参数
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        if not execution_match:
            print("❌ 未找到execution参数")
            return False
        
        execution = execution_match.group(1)
        print(f"execution: {execution[:50]}...")
        
        # 步骤2：提交登录表单（禁用自动重定向）
        print("\n2. 提交登录表单...")
        
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": "submit",
            "geolocation": ""
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_url,
            "Origin": "http://cas.hnu.edu.cn"
        }
        
        # 禁用自动重定向，手动处理
        login_response = session.post(
            login_url,
            data=login_data,
            headers=headers,
            timeout=15,
            allow_redirects=False  # 关键：禁用自动重定向
        )
        
        print(f"登录响应状态码: {login_response.status_code}")
        print(f"响应头: {dict(login_response.headers)}")

        # 如果是403，检查响应内容
        if login_response.status_code == 403:
            print(f"❌ 登录被拒绝 (403)")
            print(f"响应内容长度: {len(login_response.text)}")

            # 保存403响应内容用于分析
            with open("login_403_response.html", "w", encoding="utf-8") as f:
                f.write(login_response.text)
            print("已保存403响应内容到 login_403_response.html")

            # 检查常见错误信息
            if "用户名或密码错误" in login_response.text:
                print("  错误原因: 用户名或密码错误")
            elif "验证码" in login_response.text:
                print("  错误原因: 需要验证码")
            elif "账号被锁定" in login_response.text:
                print("  错误原因: 账号被锁定")
            elif "访问被拒绝" in login_response.text:
                print("  错误原因: 访问被拒绝")
            else:
                print("  未知403错误，请检查响应内容")

            return False

        # 步骤3：手动处理重定向链
        print("\n3. 手动处理重定向链...")
        
        redirect_count = 0
        current_response = login_response
        
        while current_response.status_code in [301, 302, 303, 307, 308] and redirect_count < 10:
            redirect_count += 1
            location = current_response.headers.get('Location')
            
            if not location:
                print(f"❌ 重定向 {redirect_count}: 没有Location头")
                break
            
            print(f"重定向 {redirect_count}: {location}")
            
            # 检查Cookie变化
            print(f"  当前Cookie数量: {len(session.cookies)}")
            for cookie in session.cookies:
                print(f"    {cookie.name}={cookie.value} (Domain: {cookie.domain})")
            
            # 跟随重定向
            try:
                current_response = session.get(location, timeout=15, allow_redirects=False)
                print(f"  响应状态码: {current_response.status_code}")
                
                # 如果到达目标域名，说明登录成功
                if "eportal.hnu.edu.cn" in location:
                    print(f"  ✅ 到达目标系统: {location}")
                    
                    # 继续跟随重定向直到最终页面
                    if current_response.status_code in [301, 302, 303, 307, 308]:
                        continue
                    else:
                        break
                        
            except Exception as e:
                print(f"  ❌ 重定向失败: {e}")
                break
        
        # 步骤4：检查最终状态
        print(f"\n4. 最终状态检查...")
        print(f"重定向次数: {redirect_count}")
        print(f"最终状态码: {current_response.status_code}")
        print(f"最终URL: {current_response.url}")
        
        # 检查所有Cookie
        print(f"\n5. 最终Cookie检查...")
        all_cookies = []
        eportal_cookies = []
        
        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            all_cookies.append(cookie_str)
            
            print(f"  {cookie.name}={cookie.value}")
            print(f"    Domain: {cookie.domain}")
            print(f"    Path: {cookie.path}")
            print(f"    Secure: {cookie.secure}")
            
            # 收集目标域名的Cookie
            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)
        
        # 保存Cookie
        if all_cookies:
            all_cookie_str = "; ".join(all_cookies)
            with open("cookies_full.txt", "w") as f:
                f.write(all_cookie_str)
            print(f"\n已保存完整Cookie到 cookies_full.txt")
        
        if eportal_cookies:
            eportal_cookie_str = "; ".join(eportal_cookies)
            with open("cookies_eportal.txt", "w") as f:
                f.write(eportal_cookie_str)
            print(f"已保存目标系统Cookie到 cookies_eportal.txt")
            return True
        else:
            print("❌ 未获取到目标系统的Cookie")
            return False
            
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_with_eportal_cookie():
    """使用目标系统Cookie测试API"""
    print("\n=== API测试（使用目标系统Cookie）===")
    
    # 优先使用目标系统Cookie
    cookie_file = "cookies_eportal.txt" if os.path.exists("cookies_eportal.txt") else "cookies_full.txt"
    
    if not os.path.exists(cookie_file):
        print("❌ 未找到Cookie文件")
        return False
    
    with open(cookie_file, "r") as f:
        cookie_str = f.read().strip()
    
    print(f"使用Cookie文件: {cookie_file}")
    print(f"Cookie内容: {cookie_str[:100]}...")
    
    # 测试API
    url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": "57",
        "start_time": "2025-01-10",
        "end_time": "2025-01-10"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": cookie_str,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ API调用成功")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('m', '未知错误')}")
            except:
                print("❌ 响应不是有效的JSON")
        
        return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行完整的CAS流程测试
    login_success = test_full_cas_flow()
    
    if login_success:
        # 测试API
        test_api_with_eportal_cookie()
    
    print("\n=== 测试完成 ===")
