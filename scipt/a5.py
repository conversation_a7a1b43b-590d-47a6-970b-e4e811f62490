import time
import json
import threading
import requests
from datetime import datetime, timedelta
import re
import asyncio
import aiohttp
import os

# 配置参数
query_interval = 0.4  # 两次查询间隔(s)
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

# Cookie持久化文件路径
COOKIE_FILE = "cookie.dat"

# 全局Cookie变量
Cookie = ""

# 多场馆配置
VENUES = {
    "南校篮球馆": {
        "resource_id": "85",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场"]
    },
    "北校篮球馆": {
        "resource_id": "84",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    },
    "楼下篮球馆": {
        "resource_id": "57",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    }
}

# 用户配置：指定要预约的场馆和场地
TARGET_VENUES = ["南校篮球馆", "楼下篮球馆"]  # 可以指定多个场馆
TARGET_VENUES = ["楼下篮球馆"]  # 可以指定多个场馆
TARGET_TIME_SLOTS = ["20"]  # 指定时间段，空列表表示所有时间段
TARGET_TIME_SLOTS = ["17"]  # 指定时间段，空列表表示所有时间段
MAX_PERIODS_PER_BOOKING = 2  # 单次预约最大时间段数量（系统限制）

current_date = datetime.now()
target_date = (current_date + timedelta(days=1)).strftime("%Y-%m-%d")
TARGET_DATE = target_date  # 目标日期，格式：YYYY-MM-DD

# 全局登录锁
login_lock = threading.Lock()
async_login_lock = asyncio.Lock()

def save_cookie(cookie):
    """保存Cookie到文件"""
    try:
        with open(COOKIE_FILE, "w") as f:
            f.write(cookie)
        print(f"✅ Cookie已保存到文件: {COOKIE_FILE}")
    except Exception as e:
        print(f"❌ 保存Cookie失败: {e}")

def load_cookie():
    """从文件加载Cookie"""
    global Cookie
    try:
        if os.path.exists(COOKIE_FILE):
            with open(COOKIE_FILE, "r") as f:
                Cookie = f.read().strip()
                print(f"✅ 从文件加载Cookie成功: {COOKIE_FILE}")
                return True
        else:
            print("⚠️ Cookie文件不存在")
            return False
    except Exception as e:
        print(f"❌ 加载Cookie失败: {e}")
        return False

def get_cookie_from_login():
    """
    通过登录获取Cookie - 最终解决方案
    """
    global Cookie

    print("⚠️ 检测到需要验证码的CAS系统")
    print("由于系统安全限制，自动登录可能失败")
    print("建议使用以下方案之一：")
    print("1. 手动登录获取Cookie")
    print("2. 使用浏览器开发者工具复制Cookie")
    print("3. 等待账号解锁后重试")

    # 提供手动Cookie输入选项
    print("\n如果您已经手动登录并获取了Cookie，可以在这里输入：")
    manual_cookie = input("请粘贴Cookie（或按回车尝试自动登录）: ").strip()

    if manual_cookie:
        print("正在验证手动输入的Cookie...")
        Cookie = manual_cookie
        save_cookie(Cookie)

        # 测试Cookie有效性
        if test_cookie_validity(Cookie):
            print("✅ 手动Cookie验证成功")
            return True
        else:
            print("❌ 手动Cookie验证失败")
            return False

    # 如果没有手动输入，尝试自动登录
    print("\n尝试自动登录...")
    return attempt_auto_login()

def test_cookie_validity(cookie_str):
    """测试Cookie有效性"""
    try:
        url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
        params = {
            "resource_id": "57",
            "start_time": "2025-01-10",
            "end_time": "2025-01-10"
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": cookie_str,
            "Accept": "application/json, text/plain, */*",
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if data.get('e') == 0:
                print(f"✅ Cookie有效，返回{len(data.get('d', {}))}个资源")
                return True
            else:
                print(f"❌ Cookie无效: {data.get('m')}")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ Cookie验证失败: {e}")
        return False

def attempt_auto_login():
    """尝试自动登录"""
    global Cookie

    session = requests.Session()
    session.proxies = {'http': None, 'https': None}

    # 设置完整的浏览器headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1"
    })

    try:
        print("正在获取登录页面...")
        response = session.get(login_url, timeout=15)

        if response.status_code != 200:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False

        # 解析参数
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        if not execution_match:
            print("❌ 无法获取execution参数")
            return False

        execution = execution_match.group(1)

        # 构建提交URL
        form_action_match = re.search(r'<form[^>]*action="([^"]*)"', response.text)
        if form_action_match:
            form_action = form_action_match.group(1).strip()
            if form_action.startswith('http'):
                submit_url = form_action
            elif form_action.startswith('/'):
                from urllib.parse import urljoin
                submit_url = urljoin(response.url, form_action)
            else:
                base_url_path = response.url.rsplit('/', 1)[0] + '/'
                submit_url = urljoin(base_url_path, form_action)
        else:
            submit_url = response.url

        print("正在提交登录信息...")
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": "submit",
            "geolocation": ""
        }

        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": response.url,
            "Origin": "http://cas.hnu.edu.cn"
        }

        login_response = session.post(
            submit_url,
            data=login_data,
            headers=headers,
            timeout=20,
            allow_redirects=True
        )

        print(f"登录响应状态码: {login_response.status_code}")
        print(f"最终URL: {login_response.url}")

        # 检查登录结果
        if "eportal.hnu.edu.cn" in login_response.url:
            print("✅ 自动登录成功")
            success = True
        else:
            print("❌ 自动登录失败")
            if "验证码" in login_response.text:
                print("  原因: 需要验证码")
            elif "用户名或密码错误" in login_response.text:
                print("  原因: 用户名或密码错误")
            elif "账号被锁定" in login_response.text:
                print("  原因: 账号被锁定")
            else:
                print("  原因: 未知错误")
            return False

        # 收集Cookie
        cookies = []
        eportal_cookies = []

        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            cookies.append(cookie_str)

            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)

        if eportal_cookies:
            Cookie = "; ".join(eportal_cookies)
            print("✅ 获取到目标系统Cookie")
        elif cookies:
            Cookie = "; ".join(cookies)
            print("⚠️ 只获取到CAS Cookie")
        else:
            print("❌ 未获取到任何Cookie")
            return False

        save_cookie(Cookie)
        return success

    except Exception as e:
        print(f"❌ 自动登录失败: {e}")
        return False

def get_manual_cookie_guide():
    """提供手动获取Cookie的指导"""
    print("\n=== 手动获取Cookie指导 ===")
    print("1. 打开浏览器，访问: https://eportal.hnu.edu.cn")
    print("2. 手动登录（输入用户名、密码、验证码）")
    print("3. 登录成功后，按F12打开开发者工具")
    print("4. 切换到Network（网络）标签")
    print("5. 刷新页面或访问任意功能")
    print("6. 找到任意请求，查看Request Headers")
    print("7. 复制Cookie字段的完整内容")
    print("8. 将Cookie粘贴到程序中使用")
    print("\nCookie格式示例:")
    print("JSESSIONID=ABC123...; PHPSESSID=DEF456...; other=value")
    print("=====================================\n")

def handle_login_required(response_text, venue_name):
    """
    处理需要登录的情况
    """
    if "该操作需要登录，请登录后重试" in response_text:
        print(f"[{venue_name}] 检测到需要登录，尝试重新登录...")
        with login_lock:  # 使用锁确保登录操作线程安全
            if get_cookie_from_login():
                return True
        return False
    return None

def get_court_data_from_api(resource_id, date, venue_name):
    """
    从API获取场地数据
    """
    global Cookie
    
    url = f"https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": resource_id,
        "start_time": date,
        "end_time": date
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": Cookie,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest"
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        # 检查是否需要登录
        login_result = handle_login_required(response.text, venue_name)
        if login_result is True:
            # 使用新Cookie重试请求
            headers["Cookie"] = Cookie
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
        elif login_result is False:
            print(f"[{venue_name}] 重新登录失败，无法获取数据")
            return {}
            
        data = response.json()

        print(f"[{venue_name}] API返回状态: {data.get('m', '未知')}")

        # 解析API返回的数据，构建court_data格式
        court_data = {}
        if data.get('e') == 0 and 'd' in data:
            # API返回的数据结构: {"e": 0, "m": "操作成功", "d": {"367": [...]}}
            for items in data['d'].values():
                for item in items:
                    court_name = item.get('abscissa', '')  # 场地名称在abscissa字段
                    time_slot = item.get('yaxis', '')      # 时间段在yaxis字段
                    time_id = item.get('time_id')          # 时间ID
                    sub_id = item.get('sub_id')            # 子资源ID
                    row = item.get('row')            # 子资源ID
                    status = row["status"]
                    
                    if status != 5:
                        continue
                    
                    time_slots = time_slot.split('-')
                    is_time = False
                    for time in TARGET_TIME_SLOTS:
                        for time_slot in time_slots:
                            if time in time_slot:
                                is_time = True
                    
                    if is_time == False:
                        continue
                        
                    if court_name not in court_data:
                        court_data[court_name] = []
                    court_data[court_name].append((time_id, sub_id, resource_id))
                    
        return court_data
    except Exception as e:
        print(f"[{venue_name}] 获取场地数据失败: {e}")
        return {}

def get_all_venues_data():
    """
    获取所有指定场馆的数据
    """
    all_court_data = {}

    for venue_name in TARGET_VENUES:
        if venue_name not in VENUES:
            print(f"警告: 未知场馆 {venue_name}")
            continue

        venue_config = VENUES[venue_name]
        resource_id = venue_config["resource_id"]

        print(f"正在获取 {venue_name} (ID: {resource_id}) 的场地数据...")
        venue_data = get_court_data_from_api(resource_id, TARGET_DATE, venue_name)
        print(venue_data)
        all_court_data.update(venue_data)

    if not all_court_data:
        print("未能从任何场馆获取到有效数据，使用默认配置")
        # 返回默认数据
        # all_court_data = {
        #     "南校篮球馆-1号场": [(4480, 21052, "85"), (4481, 21051, "85")],
        #     "南校篮球馆-2号场": [(4480, 21066, "85"), (4481, 21065, "85")],
        # }
    pop_keys = []
    for key ,value in all_court_data.items():
        if len(value) == 1:
            pop_keys.append(key)
    for key in pop_keys:
        print("YICCHU"+key)
        all_court_data.pop(key)
    return all_court_data

async def launch(session, court_number, async_login_lock):
    """
    异步尝试预约指定场地的时间段
    """
    global Cookie
    
    url = "https://eportal.hnu.edu.cn/site/reservation/launch"
    target_date = TARGET_DATE

    # 获取该场地的resource_id和时间段数据
    resource_id = None
    periods_to_book = court_data[court_number]
    json_data = []

    for time_id, sub_id, r_id in periods_to_book:
        if resource_id is None:
            resource_id = r_id
        json_data.append({
            "date": target_date,
            "period": time_id,
            "sub_resource_id": sub_id
        })

    print(f"[{court_number}] 预约数据 ({len(json_data)}/{len(court_data[court_number])} 个时间段): {json.dumps(json_data, ensure_ascii=False)}")

    try:
        async with session.post(
            url,
            timeout=aiohttp.ClientTimeout(total=10),
            headers={
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Cookie": Cookie,
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Requested-With":"XMLHttpRequest"
            },
            data={
                "resource_id": resource_id,
                "code": "",
                "remarks": "",
                "deduct_num": "",
                "data": json.dumps(json_data),
            },
        ) as response:
            try:
                text = await response.text()
                
                # 检查是否需要登录
                if "该操作需要登录，请登录后重试" in text:
                    print(f"[{court_number}] 检测到需要登录，尝试重新登录...")
                    async with async_login_lock:
                        # 使用线程池执行同步登录函数
                        login_success = await asyncio.to_thread(get_cookie_from_login)
                        if login_success:
                            print(f"[{court_number}] 重新登录成功，重试请求...")
                            # 使用新Cookie重试请求
                            async with session.post(
                                url,
                                timeout=aiohttp.ClientTimeout(total=10),
                                headers={
                                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                                    "Cookie": Cookie,
                                    "Accept": "application/json, text/plain, */*",
                                    "Accept-Encoding": "gzip, deflate, br, zstd",
                                    "Content-Type": "application/x-www-form-urlencoded",
                                    "X-Requested-With":"XMLHttpRequest"
                                },
                                data={
                                    "resource_id": resource_id,
                                    "code": "",
                                    "remarks": "",
                                    "deduct_num": "",
                                    "data": json.dumps(json_data),
                                },
                            ) as retry_response:
                                retry_text = await retry_response.text()
                                if "操作成功" in retry_text:
                                    print(f"✅ 预约成功！场号: {court_number}, 时间段数: {len(json_data)}")
                                    return True
                                else:
                                    print(f"❌ 重试后预约失败，场号: {court_number}, 原因: {retry_text}")
                                    return False
                        else:
                            print(f"❌ 重新登录失败，无法继续预约")
                            return False
                
                data = json.loads(text)
                if data["m"] == "操作成功":
                    print(f"✅ 预约成功！场号: {court_number}, 时间段数: {len(json_data)}")
                    return True
                else:
                    print(f"❌ 预约失败，场号: {court_number}, 原因: {data['m']}")
                    return False
            except Exception:
                print(f"❌ 解析响应失败，场号: {court_number}, 响应内容: {text}")
                return False
    except Exception as e:
        print(f"❌ [{court_number}] 发生错误: {e}")
        return False

async def request_dispatcher(session, reserved_times, stop_event, async_login_lock):
    """
    请求分发器：全局固定间隔发送请求
    """
    # 使用绝对时间点控制请求间隔
    next_request_time = time.monotonic()
    court_index = 0  # 当前要请求的场地索引
    
    while not stop_event.is_set():
        # 检查是否所有场地都已预约成功
        if len(reserved_times) == len(court_numbers):
            print("所有场地已预约成功，停止请求")
            stop_event.set()
            break
            
        # 计算到下一次请求的等待时间
        now = time.monotonic()
        wait_time = max(0, next_request_time - now)
        if wait_time > 0:
            await asyncio.sleep(wait_time)
        
        # 更新下一次请求的时间点
        next_request_time = time.monotonic() + query_interval
        
        # 选择下一个场地
        court_number = court_numbers[court_index]
        court_index = (court_index + 1) % len(court_numbers)
        
        # 跳过已预约成功的场地
        if court_number in reserved_times:
            continue
        
        # 发送异步请求
        task = asyncio.create_task(launch(session, court_number, async_login_lock))
        
        # 添加回调处理预约成功的情况
        def callback(fut):
            try:
                success = fut.result()
                if success:
                    reserved_times.add(court_number)
                    print(f"场地 {court_number} 预约成功，已添加到已预约列表")
            except Exception as e:
                print(f"回调处理错误: {e}")
        
        task.add_done_callback(callback)

async def main_async():
    """
    异步主函数
    """
    global async_login_lock
    async_login_lock = asyncio.Lock()  # 初始化异步登录锁
    
    stop_event = asyncio.Event()
    reserved_times = set()

    # 设置超时停止（120秒后停止）
    asyncio.create_task(stop_after_timeout(stop_event, 120))

    async with aiohttp.ClientSession() as session:
        await request_dispatcher(session, reserved_times, stop_event, async_login_lock)

async def stop_after_timeout(stop_event, timeout):
    """
    异步超时停止函数
    """
    await asyncio.sleep(timeout)
    stop_event.set()
    print("运行时间到达限制，停止轮询")

def main():
    """
    主函数：先登录获取Cookie，然后执行异步轮询
    """
    global court_data, court_numbers, Cookie

    print("=== 单线程轮询预约系统 ===")
    print(f"目标场馆: {TARGET_VENUES}")
    print(f"目标时间段: {TARGET_TIME_SLOTS if TARGET_TIME_SLOTS else '所有时间段'}")
    print(f"目标日期: {TARGET_DATE}")
    print(f"单次预约最大时间段数: {MAX_PERIODS_PER_BOOKING}")
    print(f"请求间隔: {query_interval}秒")
    print(f"Cookie存储文件: {COOKIE_FILE}")
    print("=" * 40)

    # 第一步：尝试从文件加载Cookie
    print("\n=== 尝试加载Cookie ===")
    if not load_cookie():
        print("⚠️ 未找到有效的Cookie文件，需要重新登录")
        get_manual_cookie_guide()  # 显示手动获取指导

        # 如果加载失败，尝试登录获取
        if not get_cookie_from_login():
            print("❌ 登录失败，程序退出")
            print("\n建议:")
            print("1. 手动登录获取Cookie后重新运行程序")
            print("2. 检查账号是否被锁定")
            print("3. 等待一段时间后重试")
            return
    else:
        print(f"✅ 使用存储的Cookie: {Cookie[:50]}...")  # 只显示前50个字符

        # 验证Cookie有效性
        if not test_cookie_validity(Cookie):
            print("❌ 当前Cookie已失效，需要重新获取")
            get_manual_cookie_guide()

            if not get_cookie_from_login():
                print("❌ 无法获取有效的Cookie，程序退出")
                return

    print("\n=== 开始获取场地数据 ===")
    court_data = get_all_venues_data()
    court_numbers = list(court_data.keys())
    print(f"获取到的场地: {court_numbers}")

    if not court_numbers:
        print("❌ 未获取到任何可用场地，程序退出")
        return

    print("\n=== 开始轮询预约 ===")
    asyncio.run(main_async())
    print("程序已退出。")

def test_cookie():
    """
    测试当前Cookie是否有效
    """
    if not Cookie:
        print("❌ 没有可用的Cookie")
        return False

    try:
        print("正在测试Cookie有效性...")

        # 测试API调用
        url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
        params = {
            "resource_id": "57",  # 楼下篮球馆
            "start_time": "2025-01-10",
            "end_time": "2025-01-10"
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": Cookie,
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)

        print(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ Cookie有效，API调用成功")
                    print(f"返回数据: {len(data.get('d', {}))}个资源")
                    return True
                else:
                    error_msg = data.get('m', '未知错误')
                    print(f"❌ Cookie可能已失效: {error_msg}")
                    return False
            except:
                print("❌ 响应格式错误")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 测试Cookie时发生错误: {e}")
        return False

def safe_test():
    """
    安全测试函数 - 检查Cookie状态并谨慎处理登录
    """
    global Cookie

    print("=== Cookie状态检查 ===")

    # 首先尝试从文件加载Cookie
    if load_cookie():
        print("✅ 从文件加载了Cookie")
        print(f"Cookie内容: {Cookie[:100]}...")

        if test_cookie():
            print("✅ 现有Cookie有效")
            return True
        else:
            print("❌ 现有Cookie已失效")
    else:
        print("❌ 没有保存的Cookie")

    # 检查是否应该尝试重新登录
    import time
    import os

    last_login_file = "last_login_time.txt"
    current_time = time.time()

    if os.path.exists(last_login_file):
        with open(last_login_file, "r") as f:
            try:
                last_login_time = float(f.read().strip())
                time_diff = current_time - last_login_time

                if time_diff < 300:  # 5分钟内不重复登录
                    print(f"⚠️ 距离上次登录仅{time_diff:.0f}秒，为避免账号锁定，暂不重新登录")
                    print("建议:")
                    print("1. 等待5分钟后再试")
                    print("2. 检查账号是否被锁定")
                    print("3. 手动登录网站确认状态")
                    return False
            except:
                pass

    print("\n⚠️ 需要重新登录获取Cookie")
    print("注意: 频繁登录可能导致账号被锁定")

    user_input = input("是否继续尝试登录? (y/N): ").strip().lower()
    if user_input != 'y':
        print("已取消登录尝试")
        return False

    # 尝试登录
    print("\n正在尝试登录...")
    if get_cookie_from_login():
        # 记录登录时间
        with open(last_login_file, "w") as f:
            f.write(str(current_time))

        # 再次测试Cookie
        if test_cookie():
            print("✅ 新Cookie获取成功且有效")
            return True
        else:
            print("❌ 新Cookie获取成功但无效")
            return False
    else:
        print("❌ 登录失败")
        return False

if __name__ == "__main__":
    # 如果直接运行此脚本，执行安全测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        safe_test()
    else:
        main()