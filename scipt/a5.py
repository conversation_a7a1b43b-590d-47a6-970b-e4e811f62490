import time
import json
import threading
import requests
from datetime import datetime, timedelta
import re
import asyncio
import aiohttp
import os

# 配置参数
query_interval = 0.4  # 两次查询间隔(s)
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

# Cookie持久化文件路径
COOKIE_FILE = "cookie.dat"

# 全局Cookie变量
Cookie = ""

# 多场馆配置
VENUES = {
    "南校篮球馆": {
        "resource_id": "85",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场"]
    },
    "北校篮球馆": {
        "resource_id": "84",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    },
    "楼下篮球馆": {
        "resource_id": "57",
        "courts": ["1号场", "2号场", "3号场", "4号场", "5号场", "6号场", "7号场", "8号场"]
    }
}

# 用户配置：指定要预约的场馆和场地
TARGET_VENUES = ["南校篮球馆", "楼下篮球馆"]  # 可以指定多个场馆
TARGET_VENUES = ["楼下篮球馆"]  # 可以指定多个场馆
TARGET_TIME_SLOTS = ["20"]  # 指定时间段，空列表表示所有时间段
TARGET_TIME_SLOTS = ["17"]  # 指定时间段，空列表表示所有时间段
MAX_PERIODS_PER_BOOKING = 2  # 单次预约最大时间段数量（系统限制）

current_date = datetime.now()
target_date = (current_date + timedelta(days=1)).strftime("%Y-%m-%d")
TARGET_DATE = target_date  # 目标日期，格式：YYYY-MM-DD

# 全局登录锁
login_lock = threading.Lock()
async_login_lock = asyncio.Lock()

def save_cookie(cookie):
    """保存Cookie到文件"""
    try:
        with open(COOKIE_FILE, "w") as f:
            f.write(cookie)
        print(f"✅ Cookie已保存到文件: {COOKIE_FILE}")
    except Exception as e:
        print(f"❌ 保存Cookie失败: {e}")

def load_cookie():
    """从文件加载Cookie"""
    global Cookie
    try:
        if os.path.exists(COOKIE_FILE):
            with open(COOKIE_FILE, "r") as f:
                Cookie = f.read().strip()
                print(f"✅ 从文件加载Cookie成功: {COOKIE_FILE}")
                return True
        else:
            print("⚠️ Cookie文件不存在")
            return False
    except Exception as e:
        print(f"❌ 加载Cookie失败: {e}")
        return False

def get_cookie_from_login():
    """
    通过登录获取Cookie - 改进版本，包含错误处理和安全检查
    """
    global Cookie

    session = requests.Session()

    # 禁用代理，直接连接
    session.proxies = {
        'http': None,
        'https': None
    }

    try:
        # 第一步：检查是否需要等待（避免频繁请求）
        print("正在检查登录状态...")

        # 第二步：访问登录页面获取必要的参数
        print("正在访问登录页面...")
        login_response = session.get(login_url, timeout=15)

        # 检查响应状态
        if login_response.status_code != 200:
            print(f"❌ 登录页面访问失败，状态码: {login_response.status_code}")
            return False

        print(f"登录页面状态码: {login_response.status_code}")
        print(f"登录页面URL: {login_response.url}")

        # 检查页面是否有错误信息
        if "系统维护" in login_response.text:
            print("❌ 系统正在维护中")
            return False
        elif "访问过于频繁" in login_response.text:
            print("❌ 访问过于频繁，请稍后再试")
            return False

        # 从登录页面提取必要的参数
        execution_match = re.search(r'name="execution" value="([^"]*)"', login_response.text)
        event_id_match = re.search(r'name="_eventId" value="([^"]*)"', login_response.text)

        if not execution_match:
            print("❌ 无法获取execution参数，可能页面结构已变化")
            return False

        execution = execution_match.group(1)
        event_id = event_id_match.group(1) if event_id_match else "submit"

        print(f"获取到execution参数")
        print(f"获取到_eventId: {event_id}")

        # 第三步：谨慎提交登录表单
        print("正在提交登录信息...")
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": event_id,
            "geolocation": ""
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_url,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "max-age=0",
            "Upgrade-Insecure-Requests": "1",
            "Origin": "http://cas.hnu.edu.cn"
        }

        # 使用较长的超时时间，允许完整的重定向
        login_submit_response = session.post(
            login_url,
            data=login_data,
            headers=headers,
            timeout=30,
            allow_redirects=True
        )

        # 第四步：检查登录结果
        print(f"登录响应状态码: {login_submit_response.status_code}")
        print(f"最终URL: {login_submit_response.url}")

        # 检查是否出现错误
        if login_submit_response.status_code == 403:
            print("❌ 登录被拒绝 (403)")
            if "账号被锁定" in login_submit_response.text:
                print("❌ 账号被锁定，请稍后再试或联系管理员")
            elif "用户名或密码错误" in login_submit_response.text:
                print("❌ 用户名或密码错误")
            elif "验证码" in login_submit_response.text:
                print("❌ 需要验证码")
            else:
                print("❌ 未知的403错误")
            return False

        # 检查是否成功重定向到目标系统
        success = False
        if "eportal.hnu.edu.cn" in login_submit_response.url:
            print("✅ 登录成功，已重定向到目标系统")
            success = True
        elif "cas.hnu.edu.cn" in login_submit_response.url and "login" in login_submit_response.url:
            print("❌ 登录失败，仍在CAS登录页面")
            # 检查具体错误信息
            if "用户名或密码错误" in login_submit_response.text:
                print("❌ 用户名或密码错误")
            elif "验证码" in login_submit_response.text:
                print("❌ 需要验证码")
            elif "闲置时间过长" in login_submit_response.text:
                print("❌ 页面闲置时间过长")
            else:
                print("❌ 未知登录错误")
            return False

        # 第五步：收集所有Cookie
        print("正在收集Cookie...")
        cookies = []
        eportal_cookies = []

        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            cookies.append(cookie_str)

            print(f"Cookie: {cookie.name}={cookie.value} (Domain: {cookie.domain})")

            # 特别关注目标系统的Cookie
            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)

        if cookies:
            # 优先使用目标系统的Cookie，如果没有则使用所有Cookie
            if eportal_cookies:
                Cookie = "; ".join(eportal_cookies)
                print("✅ 成功获取目标系统Cookie:")
            else:
                Cookie = "; ".join(cookies)
                print("✅ 成功获取Cookie（包含CAS Cookie）:")

            print(f"Cookie: {Cookie}")

            # 保存新获取的Cookie
            save_cookie(Cookie)
            return success
        else:
            print("❌ 未能获取到任何Cookie")
            return False

    except requests.exceptions.Timeout:
        print("❌ 登录请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接错误")
        return False
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        return False

def handle_login_required(response_text, venue_name):
    """
    处理需要登录的情况
    """
    if "该操作需要登录，请登录后重试" in response_text:
        print(f"[{venue_name}] 检测到需要登录，尝试重新登录...")
        with login_lock:  # 使用锁确保登录操作线程安全
            if get_cookie_from_login():
                return True
        return False
    return None

def get_court_data_from_api(resource_id, date, venue_name):
    """
    从API获取场地数据
    """
    global Cookie
    
    url = f"https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": resource_id,
        "start_time": date,
        "end_time": date
    }

    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": Cookie,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest"
    }

    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        # 检查是否需要登录
        login_result = handle_login_required(response.text, venue_name)
        if login_result is True:
            # 使用新Cookie重试请求
            headers["Cookie"] = Cookie
            response = requests.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
        elif login_result is False:
            print(f"[{venue_name}] 重新登录失败，无法获取数据")
            return {}
            
        data = response.json()

        print(f"[{venue_name}] API返回状态: {data.get('m', '未知')}")

        # 解析API返回的数据，构建court_data格式
        court_data = {}
        if data.get('e') == 0 and 'd' in data:
            # API返回的数据结构: {"e": 0, "m": "操作成功", "d": {"367": [...]}}
            for items in data['d'].values():
                for item in items:
                    court_name = item.get('abscissa', '')  # 场地名称在abscissa字段
                    time_slot = item.get('yaxis', '')      # 时间段在yaxis字段
                    time_id = item.get('time_id')          # 时间ID
                    sub_id = item.get('sub_id')            # 子资源ID
                    row = item.get('row')            # 子资源ID
                    status = row["status"]
                    
                    if status != 5:
                        continue
                    
                    time_slots = time_slot.split('-')
                    is_time = False
                    for time in TARGET_TIME_SLOTS:
                        for time_slot in time_slots:
                            if time in time_slot:
                                is_time = True
                    
                    if is_time == False:
                        continue
                        
                    if court_name not in court_data:
                        court_data[court_name] = []
                    court_data[court_name].append((time_id, sub_id, resource_id))
                    
        return court_data
    except Exception as e:
        print(f"[{venue_name}] 获取场地数据失败: {e}")
        return {}

def get_all_venues_data():
    """
    获取所有指定场馆的数据
    """
    all_court_data = {}

    for venue_name in TARGET_VENUES:
        if venue_name not in VENUES:
            print(f"警告: 未知场馆 {venue_name}")
            continue

        venue_config = VENUES[venue_name]
        resource_id = venue_config["resource_id"]

        print(f"正在获取 {venue_name} (ID: {resource_id}) 的场地数据...")
        venue_data = get_court_data_from_api(resource_id, TARGET_DATE, venue_name)
        print(venue_data)
        all_court_data.update(venue_data)

    if not all_court_data:
        print("未能从任何场馆获取到有效数据，使用默认配置")
        # 返回默认数据
        # all_court_data = {
        #     "南校篮球馆-1号场": [(4480, 21052, "85"), (4481, 21051, "85")],
        #     "南校篮球馆-2号场": [(4480, 21066, "85"), (4481, 21065, "85")],
        # }
    pop_keys = []
    for key ,value in all_court_data.items():
        if len(value) == 1:
            pop_keys.append(key)
    for key in pop_keys:
        print("YICCHU"+key)
        all_court_data.pop(key)
    return all_court_data

async def launch(session, court_number, async_login_lock):
    """
    异步尝试预约指定场地的时间段
    """
    global Cookie
    
    url = "https://eportal.hnu.edu.cn/site/reservation/launch"
    target_date = TARGET_DATE

    # 获取该场地的resource_id和时间段数据
    resource_id = None
    periods_to_book = court_data[court_number]
    json_data = []

    for time_id, sub_id, r_id in periods_to_book:
        if resource_id is None:
            resource_id = r_id
        json_data.append({
            "date": target_date,
            "period": time_id,
            "sub_resource_id": sub_id
        })

    print(f"[{court_number}] 预约数据 ({len(json_data)}/{len(court_data[court_number])} 个时间段): {json.dumps(json_data, ensure_ascii=False)}")

    try:
        async with session.post(
            url,
            timeout=aiohttp.ClientTimeout(total=10),
            headers={
                "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Cookie": Cookie,
                "Accept": "application/json, text/plain, */*",
                "Accept-Encoding": "gzip, deflate, br, zstd",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Requested-With":"XMLHttpRequest"
            },
            data={
                "resource_id": resource_id,
                "code": "",
                "remarks": "",
                "deduct_num": "",
                "data": json.dumps(json_data),
            },
        ) as response:
            try:
                text = await response.text()
                
                # 检查是否需要登录
                if "该操作需要登录，请登录后重试" in text:
                    print(f"[{court_number}] 检测到需要登录，尝试重新登录...")
                    async with async_login_lock:
                        # 使用线程池执行同步登录函数
                        login_success = await asyncio.to_thread(get_cookie_from_login)
                        if login_success:
                            print(f"[{court_number}] 重新登录成功，重试请求...")
                            # 使用新Cookie重试请求
                            async with session.post(
                                url,
                                timeout=aiohttp.ClientTimeout(total=10),
                                headers={
                                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                                    "Cookie": Cookie,
                                    "Accept": "application/json, text/plain, */*",
                                    "Accept-Encoding": "gzip, deflate, br, zstd",
                                    "Content-Type": "application/x-www-form-urlencoded",
                                    "X-Requested-With":"XMLHttpRequest"
                                },
                                data={
                                    "resource_id": resource_id,
                                    "code": "",
                                    "remarks": "",
                                    "deduct_num": "",
                                    "data": json.dumps(json_data),
                                },
                            ) as retry_response:
                                retry_text = await retry_response.text()
                                if "操作成功" in retry_text:
                                    print(f"✅ 预约成功！场号: {court_number}, 时间段数: {len(json_data)}")
                                    return True
                                else:
                                    print(f"❌ 重试后预约失败，场号: {court_number}, 原因: {retry_text}")
                                    return False
                        else:
                            print(f"❌ 重新登录失败，无法继续预约")
                            return False
                
                data = json.loads(text)
                if data["m"] == "操作成功":
                    print(f"✅ 预约成功！场号: {court_number}, 时间段数: {len(json_data)}")
                    return True
                else:
                    print(f"❌ 预约失败，场号: {court_number}, 原因: {data['m']}")
                    return False
            except Exception:
                print(f"❌ 解析响应失败，场号: {court_number}, 响应内容: {text}")
                return False
    except Exception as e:
        print(f"❌ [{court_number}] 发生错误: {e}")
        return False

async def request_dispatcher(session, reserved_times, stop_event, async_login_lock):
    """
    请求分发器：全局固定间隔发送请求
    """
    # 使用绝对时间点控制请求间隔
    next_request_time = time.monotonic()
    court_index = 0  # 当前要请求的场地索引
    
    while not stop_event.is_set():
        # 检查是否所有场地都已预约成功
        if len(reserved_times) == len(court_numbers):
            print("所有场地已预约成功，停止请求")
            stop_event.set()
            break
            
        # 计算到下一次请求的等待时间
        now = time.monotonic()
        wait_time = max(0, next_request_time - now)
        if wait_time > 0:
            await asyncio.sleep(wait_time)
        
        # 更新下一次请求的时间点
        next_request_time = time.monotonic() + query_interval
        
        # 选择下一个场地
        court_number = court_numbers[court_index]
        court_index = (court_index + 1) % len(court_numbers)
        
        # 跳过已预约成功的场地
        if court_number in reserved_times:
            continue
        
        # 发送异步请求
        task = asyncio.create_task(launch(session, court_number, async_login_lock))
        
        # 添加回调处理预约成功的情况
        def callback(fut):
            try:
                success = fut.result()
                if success:
                    reserved_times.add(court_number)
                    print(f"场地 {court_number} 预约成功，已添加到已预约列表")
            except Exception as e:
                print(f"回调处理错误: {e}")
        
        task.add_done_callback(callback)

async def main_async():
    """
    异步主函数
    """
    global async_login_lock
    async_login_lock = asyncio.Lock()  # 初始化异步登录锁
    
    stop_event = asyncio.Event()
    reserved_times = set()

    # 设置超时停止（120秒后停止）
    asyncio.create_task(stop_after_timeout(stop_event, 120))

    async with aiohttp.ClientSession() as session:
        await request_dispatcher(session, reserved_times, stop_event, async_login_lock)

async def stop_after_timeout(stop_event, timeout):
    """
    异步超时停止函数
    """
    await asyncio.sleep(timeout)
    stop_event.set()
    print("运行时间到达限制，停止轮询")

def main():
    """
    主函数：先登录获取Cookie，然后执行异步轮询
    """
    global court_data, court_numbers, Cookie

    print("=== 单线程轮询预约系统 ===")
    print(f"目标场馆: {TARGET_VENUES}")
    print(f"目标时间段: {TARGET_TIME_SLOTS if TARGET_TIME_SLOTS else '所有时间段'}")
    print(f"目标日期: {TARGET_DATE}")
    print(f"单次预约最大时间段数: {MAX_PERIODS_PER_BOOKING}")
    print(f"请求间隔: {query_interval}秒")
    print(f"Cookie存储文件: {COOKIE_FILE}")
    print("=" * 40)

    # 第一步：尝试从文件加载Cookie
    print("\n=== 尝试加载Cookie ===")
    if not load_cookie():
        print("⚠️ 未找到有效的Cookie文件，需要重新登录")
        # 如果加载失败，尝试登录获取
        if not get_cookie_from_login():
            print("❌ 登录失败，程序退出")
            return
    else:
        print(f"✅ 使用存储的Cookie: {Cookie[:50]}...")  # 只显示前50个字符

    print("\n=== 开始获取场地数据 ===")
    court_data = get_all_venues_data()
    court_numbers = list(court_data.keys())
    print(f"获取到的场地: {court_numbers}")

    if not court_numbers:
        print("❌ 未获取到任何可用场地，程序退出")
        return

    print("\n=== 开始轮询预约 ===")
    asyncio.run(main_async())
    print("程序已退出。")

def test_cookie():
    """
    测试当前Cookie是否有效
    """
    if not Cookie:
        print("❌ 没有可用的Cookie")
        return False

    try:
        print("正在测试Cookie有效性...")

        # 测试API调用
        url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
        params = {
            "resource_id": "57",  # 楼下篮球馆
            "start_time": "2025-01-10",
            "end_time": "2025-01-10"
        }

        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": Cookie,
            "Accept": "application/json, text/plain, */*",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
        }

        response = requests.get(url, params=params, headers=headers, timeout=10)

        print(f"API响应状态码: {response.status_code}")

        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ Cookie有效，API调用成功")
                    print(f"返回数据: {len(data.get('d', {}))}个资源")
                    return True
                else:
                    error_msg = data.get('m', '未知错误')
                    print(f"❌ Cookie可能已失效: {error_msg}")
                    return False
            except:
                print("❌ 响应格式错误")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            return False

    except Exception as e:
        print(f"❌ 测试Cookie时发生错误: {e}")
        return False

def safe_test():
    """
    安全测试函数 - 检查Cookie状态并谨慎处理登录
    """
    global Cookie

    print("=== Cookie状态检查 ===")

    # 首先尝试从文件加载Cookie
    if load_cookie():
        print("✅ 从文件加载了Cookie")
        print(f"Cookie内容: {Cookie[:100]}...")

        if test_cookie():
            print("✅ 现有Cookie有效")
            return True
        else:
            print("❌ 现有Cookie已失效")
    else:
        print("❌ 没有保存的Cookie")

    # 检查是否应该尝试重新登录
    import time
    import os

    last_login_file = "last_login_time.txt"
    current_time = time.time()

    if os.path.exists(last_login_file):
        with open(last_login_file, "r") as f:
            try:
                last_login_time = float(f.read().strip())
                time_diff = current_time - last_login_time

                if time_diff < 300:  # 5分钟内不重复登录
                    print(f"⚠️ 距离上次登录仅{time_diff:.0f}秒，为避免账号锁定，暂不重新登录")
                    print("建议:")
                    print("1. 等待5分钟后再试")
                    print("2. 检查账号是否被锁定")
                    print("3. 手动登录网站确认状态")
                    return False
            except:
                pass

    print("\n⚠️ 需要重新登录获取Cookie")
    print("注意: 频繁登录可能导致账号被锁定")

    user_input = input("是否继续尝试登录? (y/N): ").strip().lower()
    if user_input != 'y':
        print("已取消登录尝试")
        return False

    # 尝试登录
    print("\n正在尝试登录...")
    if get_cookie_from_login():
        # 记录登录时间
        with open(last_login_file, "w") as f:
            f.write(str(current_time))

        # 再次测试Cookie
        if test_cookie():
            print("✅ 新Cookie获取成功且有效")
            return True
        else:
            print("❌ 新Cookie获取成功但无效")
            return False
    else:
        print("❌ 登录失败")
        return False

if __name__ == "__main__":
    # 如果直接运行此脚本，执行安全测试
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        safe_test()
    else:
        main()