<!DOCTYPE html>





<html>

<head>
	<meta charset="utf-8" />
	<title>
		统一身份认证平台
	</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="stylesheet" href="assets/plugins/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" href="assets/css/style.css?v=0.44969520037396926">
	<link rel="stylesheet" href="assets/fonts/font-awesome-4.4.0/css/font-awesome.css" />
	<link rel="stylesheet" href="assets/text-security/text-security.css" />
	<link rel="stylesheet" href="assets/text-security/text-security-login.css" />
	<link rel="stylesheet" href="css/tac.css">

	<script type='text/javascript' src='js/qrcode/qrcode.js'></script>
	<script type='text/javascript' src="js/jquery/jquery-1.11.1-min.js"></script>
	<script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.9.js"></script>
	<script type="text/javascript" src="js/layer/layer.js"></script>
	<script type="text/javascript" src="js/i18n.js"></script>
	<script type='text/javascript' src="js/login/security.js?v=0.6077614097067795"></script>
	<script type="text/javascript" src="js/login/pwdYz.js?v=0.3011738940523513"></script>
	<script type="text/javascript" src="js/login/login.js?v=0.6399015503036047"></script>
	<script src="js/login/tac.min.js"></script>
	
<style>
		.main {
	    		display: flex;
    			align-items: center;
    			padding: 30px 0;
		}
		.main .login-container {
	    		position: relative;
        		min-height: 340px;
        		height: auto;
        		margin: 0 0 0 30px;
		}
		.login-form {
			padding-bottom: 0;
		}
		.cjgzs-class {
	    		margin-top: 35px;
	        	position: relative;
    			bottom: 0;
		}
		.login-info .error {
			position: relative;
			top: 0;
		}
		@media (max-width: 768px) {
			.main {
				justify-content: center;
	        		padding: 0;
			}
			.login-container {
				margin: 0!important;
				flex: 1;
			}
			.login-box {
				min-height: auto !important;
			}
			.code-img {
				width: 33%;
			}
		}
	</style>
	
</head>

<body id="login_new">
<!DOCTYPE html>

<script type="text/javascript">
    //判断操作系统和浏览器类型，需要判断的页面加上detectOS()；
    function detectOS() {
        var sUserAgent = navigator.userAgent;
        var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
        if (isWin) {
            if(navigator.userAgent.indexOf("MSIE 6.0")>0
                || navigator.userAgent.indexOf("MSIE 7.0")>0
                || navigator.userAgent.indexOf("MSIE 8.0")>0){ //IE6、7、8、9
                window.location.href = "https://cas.hnu.edu.cn/system/browser.zf";
            }
        }
    }
    detectOS();
</script>
<html>
<head>
    <meta charset="utf-8" />
</head>
<body></body>
</html>
<!--<input id="send_error" type="hidden" value="loginView.sendsms.error "/>
-->









<div class="login-page">
	<div class="top_logo">
				<img src="assets/images/logo.png" class="">
			</div>
	<!--<div style="padding:10px 20px;text-align:right;font-size:14px">
				<a href="login?v=0.5978342515054016&locale=zh_CN" style="color:#fff;">中文 | </a>
				<a href="login?v=0.5978342515054016&locale=en" style="color:#fff;">English </a>open
			</div>-->
	<div class="main">
	<div class="login-container">
		<div class="login-box col-xs-12">
			<div class="login-header" style="display:none;">
				<div>
					<a href="#qcode">
						<img class="hidden-xs" src="assets/images/qcode-new.png">
						<img class="hidden-lg hidden-md hidden-sm" src="assets/images/qcode-new.png">
					</a>
				</div>
				<div style="display:none;">
					<a href="#qcodcasepc"><img src="assets/images/qcode-pc.png"></a>
				</div>
				<input type="hidden" id="uuid" value="" />
				<input type="hidden" id="jrhxQrcode" />
				<input type="hidden" id="jrhxImage" />
				<input type="hidden" id="transId" />
				<input type="hidden" id="baseUrl" value="http://cas.hnu.edu.cn:80/cas"/>
			</div>
			<div class="col-xs-12 login-form-wrap" >
				<div class="login-form" id="qcode">
					<div class="tabs">
						<div class="clickin" id="zhdl">账号登录</div>
						<!--<div id="sjdl" >手机号登录</div>-->
					</div>
					<!-- 图片验证码 -->

					<div class="tab-content">
						
							<div id="captcha-box" style="display:none;
											position: absolute;
										    left: 50%;
										    top: 50%;
										    align-items: center;
										    justify-content: center;
										    transform: translate(-50%, -50%);
										    z-index: 99;" ></div>
							<input type="hidden" id="casCaptValue" value="">
							<input type="hidden" id="tcYzmSfqd" value="1" />
						
						<div class="tab-pane" id="zhdl_pane" style="display:block">
							<form id="fm1" action="login?v=0.9479565803611901 " method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/user.png" ></div>
										<input id="username" name="username" class="form-control user-input" tabindex="1" placeholder="职工号/学号" type="text" value="S2410W1114" size="25" autocomplete="off"/>
									</div>
								<!--	<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group">
									<div class="input-group col-lg-9 col-md-9 col-sm-9 col-xs-9">
										<div class="input-group-addon" style="width:25px;left:9px;"><img src="assets/images/lock.png" ></div>
										<input class="form-control pwd-input my-password-field" id="ppassword" placeholder="密码"
											   tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="password" value="" size="25" autocomplete="off">
										<input hidden id="password" name="password" placeholder="密码"  value="" tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="text" value="" size="25" autocomplete="off">

											
									</div>
									<a class="forget-pwd txt" target="_blank" href="https://cas.hnu.edu.cn/V3/securitycenter/findPwd/index.zf" >忘记密码</a>
										
								</div>
								<div class="code" id="kaptcha">
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
										<div class="input-group col-xs-8">
											<div class="input-group-addon icon-verfiy" ><img src="assets/images/icon-verfiy.png" ></div>
											<input id="authcode" name="authcode" class="form-control" tabindex="2" placeholder="验证码" type="text" value="" size="10" autocomplete="off"/>
										</div>
										<div class="col-xs-4 p-0 code-img"><img id="yzmPic" onclick="javascript:refreshCode();" class="" />
											<a href="javascript:refreshCode();">
												看不清 ? </a>
										</div>
									</div>
								</div>
								<div class="login-info">
									<p class="error text-left" id="errormsg">
										<span id="msg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="remember-me ">
										<input class="default-checkbox checkbox" type="checkbox" name="rememberMe" value="true"  id="rember">
										<!--<label for="rember">记住我 </label>-->
										<label for="rember">记住账号密码</label>
									</div>
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0 login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
						<div class="tab-pane" id="sjdl_pane" style="display:none;">
							<form id="fm2" action="/cas/login?v=0.5978342515054016" method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-smsmsgxs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/icon-use.png" ></div>
										<input id="phone" name="username" class="form-control user-input" tabindex="1" placeholder="输入手机号码" type="text" value="S2410W1114" size="25" autocomplete="off"/>

									</div>
									<!--<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group" id="phoneCard">
									<div class="input-group col-lg-7 col-md-7 col-sm-7 col-xs-7">
										
										<div class="input-group-addon icon-verfiy"><img src="assets/images/icon-verfiy.png" ></div>
										<input id="mobileCode" name="mobileCode" class="form-control pwd-input" tabindex="2" placeholder="输入短信验证码" type="password" value="" size="25" autocomplete="off"/>
										<input id="password" name="password" class="form-control pwd-input" tabindex="2" style="display: none;" type="password" value="" size="25" autocomplete="off"/>
									</div>

									<input type="button" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 sendBtn" value="获取短信验证码" id="sendsms" />
								</div>
								<div id="sumbit">
									<p class="error text-left"id="smsErrorMsg">
										<span id="smsmsg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl2">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class" style="display:none;">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType2" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType2">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
					</div>
					<div class="login-footer">
						<div class="text-center" style="display:none;">
							<!--<p class="text-left notice">
										<a href="#" class="login-info-text hidden-lg hidden-md">登录须知 </a>
									</p>-->
							
							<p>第三方账号登录</p>
							<div class="login-way">
								<a id = "wxFrame" href="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient&response_type=code&scope=snsapi_login#wechat_redirect" ><img src="assets/images/icon-weixin.png" /></a>
								<a  id = "qqFrame" href="https://graph.qq.com/oauth2.0/authorize?client_id=434242&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DQQClient&response_type=code&state=test"><img src="assets/images/icon-QQ.png" /></a>
								<a id = "ddFrame" href="https://oapi.dingtalk.com/connect/qrconnect?appid=xx&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DDingDingClient"><img src="assets/images/icon-dingding.png" /></a>
								<!--<a href="TxWxLogin_pc.html" id="txwx"><i class="fa fa-weixin"></i></a>
										<a href="https://cas.hnu.edu.cn/cas/SbkLoginUrl.html?client_name=SbkClient"><i class="fa fa-qq"></i></a>-->
								<!--<a href="javascript:void(0);"><i class="fa fa-qq"></i></a>
                                <a href="javascript:void(0);"><i class="fa fa-weixin"></i></a>-->
							</div>
						</div>
					</div>

				</div>
				<div class="qcode" id="qcodepc" style="display:none">
					<p>统一身份认证</p>
					<div id="qrcode">
						<div id="qrcode-img">
							<img id="qrcodeImg" alt="Scan me!" style="display: none;">
						</div>
					</div>
					<div id="qrcode-content">
						<div class="qrcode-bg"></div>
						<p class="refresh-text">

							<a href="#" id="qrcode_refresh"><i class="fa fa-refresh"></i>刷新 </a>
						</p>
					</div>
					<!--		扫码成功提示模态框			-->
					<div id="qrcode-success" style="width: 190px;height: 190px;position: absolute;top: 62px;left: 50%;margin-left: -95px;background: rgba(255, 255, 255, 0.9);text-align: center;display: none;">
						<div style="width: 106px; height: 106px; margin: 0 auto; margin-top: 28px">
							<img id="qrcodeSuccess" src="images/smsuccess.png" style="width: 106px; font-size: 0;">
						</div>

						<span class="success-text" style="display: inline-block; color: #000; font-size: 18px; font-weight: 700">扫码成功</span>
					</div>
					<span>打开正方云app<br />在【首页】页面左上角打开扫一扫</span>
				</div>
			</div>
		</div>
	</div>
	

<div class="rwm" style="padding-top: 90px;width: 350px;float: right;height: 400px;overflow: hidden;">
	<h4 style="text-align: center;">微信扫码登录</h4>
	<div id="wxLogin"></div>
</div>
<!--
<div class="rwm" style="padding-top:90px;"><iframe id="wxFrame" src="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=http%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient%26service%3Dhttps%3A%2F%2Fpt.hnu.edu.cn%2F&response_type=code&scope=snsapi_login#wechat_redirect
				"></iframe></div>
  -->
</div>
	<div class="footer">
               <p>湖南大学版权所有©2020年    通讯地址:湖南省长沙市岳麓区麓山南路麓山门    邮编：410082   </p>
               <p>湖南大学 湘ICP备09007699号 湘教QS3-200503-000481 湘教QS4-201312-010059 技术服务电话：88821520</p>
            </div>
</div>
<script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
<script>

	$(function () {
		    var url = "http://cas.hnu.edu.cn/cas/login?client_name=WeiXinClient";
		    var service = GetQueryString("service");
		    console.log('service: ', service);
		    if (service) {
				  url = url + '&service='+service;
			}
			var wxObj = new WxLogin({
				self_redirect: false,
				id: "wxLogin", 
				appid: "wx6bec8ae941e46eef", 
				scope: "snsapi_login", 
				redirect_uri: url,
				stylelite: 1
			});

		
		/*
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#wxFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=WeiXinClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#wxFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('wxFrame').href=$('#wxFrame').attr('href');
			},100);
		}
		*/
	})
	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#qqFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=QQClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#qqFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('qqFrame').href=$('#qqFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#ddFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("client_name");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href );
			var srcStr = pre + "&redirect_uri=" + ser + "%26" + endStr;
			$('#ddFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('ddFrame').href=$('#ddFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if(service!=null){
			$('#txwx').attr('href','TxWxLogin_pc.html?service='+service);
		}
	});
	function GetQueryString(name) {
		var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if(r!=null){
			return  unescape(r[2]);
		}else{
			return null;
		}
	}
</script>
</body>

</html>