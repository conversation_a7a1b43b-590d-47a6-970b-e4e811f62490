<!DOCTYPE html>





<html>

<head>
	<meta charset="utf-8" />
	<title>
		统一身份认证平台
	</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport"
		  content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
	<link rel="stylesheet" href="assets/plugins/bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" href="assets/css/style.css?v=0.14115016996508944">
	<link rel="stylesheet" href="assets/fonts/font-awesome-4.4.0/css/font-awesome.css" />
	<link rel="stylesheet" href="assets/text-security/text-security.css" />
	<link rel="stylesheet" href="assets/text-security/text-security-login.css" />
	<link rel="stylesheet" href="css/tac.css">

	<script type='text/javascript' src='js/qrcode/qrcode.js'></script>
	<script type='text/javascript' src="js/jquery/jquery-1.11.1-min.js"></script>
	<script type="text/javascript" src="js/jquery/jquery.i18n.properties-1.0.9.js"></script>
	<script type="text/javascript" src="js/layer/layer.js"></script>
	<script type="text/javascript" src="js/i18n.js"></script>
	<script type='text/javascript' src="js/login/security.js?v=0.9306173341793376"></script>
	<script type="text/javascript" src="js/login/pwdYz.js?v=0.42155563237181704"></script>
	<script type="text/javascript" src="js/login/login.js?v=0.8855674949049073"></script>
	<script src="js/login/tac.min.js"></script>
	
<style>
		.main {
	    		display: flex;
    			align-items: center;
    			padding: 30px 0;
		}
		.main .login-container {
	    		position: relative;
        		min-height: 340px;
        		height: auto;
        		margin: 0 0 0 30px;
		}
		.login-form {
			padding-bottom: 0;
		}
		.cjgzs-class {
	    		margin-top: 35px;
	        	position: relative;
    			bottom: 0;
		}
		.login-info .error {
			position: relative;
			top: 0;
		}
		@media (max-width: 768px) {
			.main {
				justify-content: center;
	        		padding: 0;
			}
			.login-container {
				margin: 0!important;
				flex: 1;
			}
			.login-box {
				min-height: auto !important;
			}
			.code-img {
				width: 33%;
			}
		}
	</style>
	
</head>

<body id="login_new">
<!DOCTYPE html>

<script type="text/javascript">
    //判断操作系统和浏览器类型，需要判断的页面加上detectOS()；
    function detectOS() {
        var sUserAgent = navigator.userAgent;
        var isWin = (navigator.platform == "Win32") || (navigator.platform == "Windows");
        if (isWin) {
            if(navigator.userAgent.indexOf("MSIE 6.0")>0
                || navigator.userAgent.indexOf("MSIE 7.0")>0
                || navigator.userAgent.indexOf("MSIE 8.0")>0){ //IE6、7、8、9
                window.location.href = "https://cas.hnu.edu.cn/system/browser.zf";
            }
        }
    }
    detectOS();
</script>
<html>
<head>
    <meta charset="utf-8" />
</head>
<body></body>
</html>
<!--<input id="send_error" type="hidden" value="loginView.sendsms.error "/>
-->









<div class="login-page">
	<div class="top_logo">
				<img src="assets/images/logo.png" class="">
			</div>
	<!--<div style="padding:10px 20px;text-align:right;font-size:14px">
				<a href="login?v=0.8424391343538947&locale=zh_CN" style="color:#fff;">中文 | </a>
				<a href="login?v=0.8424391343538947&locale=en" style="color:#fff;">English </a>open
			</div>-->
	<div class="main">
	<div class="login-container">
		<div class="login-box col-xs-12">
			<div class="login-header" style="display:none;">
				<div>
					<a href="#qcode">
						<img class="hidden-xs" src="assets/images/qcode-new.png">
						<img class="hidden-lg hidden-md hidden-sm" src="assets/images/qcode-new.png">
					</a>
				</div>
				<div style="display:none;">
					<a href="#qcodcasepc"><img src="assets/images/qcode-pc.png"></a>
				</div>
				<input type="hidden" id="uuid" value="" />
				<input type="hidden" id="jrhxQrcode" />
				<input type="hidden" id="jrhxImage" />
				<input type="hidden" id="transId" />
				<input type="hidden" id="baseUrl" value="http://cas.hnu.edu.cn:80/cas"/>
			</div>
			<div class="col-xs-12 login-form-wrap" >
				<div class="login-form" id="qcode">
					<div class="tabs">
						<div class="clickin" id="zhdl">账号登录</div>
						<!--<div id="sjdl" >手机号登录</div>-->
					</div>
					<!-- 图片验证码 -->

					<div class="tab-content">
						
							<div id="captcha-box" style="display:none;
											position: absolute;
										    left: 50%;
										    top: 50%;
										    align-items: center;
										    justify-content: center;
										    transform: translate(-50%, -50%);
										    z-index: 99;" ></div>
							<input type="hidden" id="casCaptValue" value="">
							<input type="hidden" id="tcYzmSfqd" value="1" />
						
						<div class="tab-pane" id="zhdl_pane" style="display:block">
							<form id="fm1" action="login?v=0.4123117140748985 " method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/user.png" ></div>
										<input id="username" name="username" class="form-control user-input" tabindex="1" placeholder="职工号/学号" type="text" value="S2410W1114" size="25" autocomplete="off"/>
									</div>
								<!--	<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group">
									<div class="input-group col-lg-9 col-md-9 col-sm-9 col-xs-9">
										<div class="input-group-addon" style="width:25px;left:9px;"><img src="assets/images/lock.png" ></div>
										<input class="form-control pwd-input my-password-field" id="ppassword" placeholder="密码"
											   tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="password" value="" size="25" autocomplete="off">
										<input hidden id="password" name="password" placeholder="密码"  value="" tabindex="2" onkeyup="CreateRatePasswdReq(this);" type="text" value="" size="25" autocomplete="off">

											
									</div>
									<a class="forget-pwd txt" target="_blank" href="https://cas.hnu.edu.cn/V3/securitycenter/findPwd/index.zf" >忘记密码</a>
										
								</div>
								<div class="code" id="kaptcha">
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0">
										<div class="input-group col-xs-8">
											<div class="input-group-addon icon-verfiy" ><img src="assets/images/icon-verfiy.png" ></div>
											<input id="authcode" name="authcode" class="form-control" tabindex="2" placeholder="验证码" type="text" value="" size="10" autocomplete="off"/>
										</div>
										<div class="col-xs-4 p-0 code-img"><img id="yzmPic" onclick="javascript:refreshCode();" class="" />
											<a href="javascript:refreshCode();">
												看不清 ? </a>
										</div>
									</div>
								</div>
								<div class="login-info">
									<p class="error text-left" id="errormsg">
										<span id="msg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="remember-me ">
										<input class="default-checkbox checkbox" type="checkbox" name="rememberMe" value="true"  id="rember">
										<!--<label for="rember">记住我 </label>-->
										<label for="rember">记住账号密码</label>
									</div>
									<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 p-0 login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
						<div class="tab-pane" id="sjdl_pane" style="display:none;">
							<form id="fm2" action="/cas/login?v=0.8424391343538947" method="post">
								<div class="form-group">
									<div class="input-group col-lg-12 col-md-12 col-sm-12 col-smsmsgxs-12">
										
										<div class="input-group-addon icon-use"><img src="assets/images/icon-use.png" ></div>
										<input id="phone" name="username" class="form-control user-input" tabindex="1" placeholder="输入手机号码" type="text" value="S2410W1114" size="25" autocomplete="off"/>

									</div>
									<!--<a class="txt" target="_blank" href="https://cas.hnu.edu.cn/securitycenter/activateUser/index.zf">账号激活</a>-->
								</div>

								<div class="form-group" id="phoneCard">
									<div class="input-group col-lg-7 col-md-7 col-sm-7 col-xs-7">
										
										<div class="input-group-addon icon-verfiy"><img src="assets/images/icon-verfiy.png" ></div>
										<input id="mobileCode" name="mobileCode" class="form-control pwd-input" tabindex="2" placeholder="输入短信验证码" type="password" value="" size="25" autocomplete="off"/>
										<input id="password" name="password" class="form-control pwd-input" tabindex="2" style="display: none;" type="password" value="" size="25" autocomplete="off"/>
									</div>

									<input type="button" class="col-lg-4 col-md-4 col-sm-4 col-xs-4 sendBtn" value="获取短信验证码" id="sendsms" />
								</div>
								<div id="sumbit">
									<p class="error text-left"id="smsErrorMsg">
										<span id="smsmsg">登录页面闲置时间过长，请打开新页面</span>
									</p>
									<div class="login-button"><button type="button" class="btn btn-block btn-primary login-btn" id="dl2">登 录 </button></div>
								</div>
								<input type="hidden" name="execution" value="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" />
								<input type="hidden" name="_eventId" value="submit" />
								<div class="cjgzs-class" style="display:none;">
									<input class="default-checkbox checkbox" type="checkbox" name="cjgzsType" id="cjgzsType2" value="true">
									<!--<label for="rember">记住我 </label>-->
									<label for="cjgzsType2">已阅读并同意<a target="_blank" href="userProtocol.html">《用户服务协议》</a>跟<a target="_blank" href="privacyLife.html">《隐私政策》</a></label>
								</div>
							</form>
						</div>
					</div>
					<div class="login-footer">
						<div class="text-center" style="display:none;">
							<!--<p class="text-left notice">
										<a href="#" class="login-info-text hidden-lg hidden-md">登录须知 </a>
									</p>-->
							
							<p>第三方账号登录</p>
							<div class="login-way">
								<a id = "wxFrame" href="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient&response_type=code&scope=snsapi_login#wechat_redirect" ><img src="assets/images/icon-weixin.png" /></a>
								<a  id = "qqFrame" href="https://graph.qq.com/oauth2.0/authorize?client_id=434242&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DQQClient&response_type=code&state=test"><img src="assets/images/icon-QQ.png" /></a>
								<a id = "ddFrame" href="https://oapi.dingtalk.com/connect/qrconnect?appid=xx&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=https%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DDingDingClient"><img src="assets/images/icon-dingding.png" /></a>
								<!--<a href="TxWxLogin_pc.html" id="txwx"><i class="fa fa-weixin"></i></a>
										<a href="https://cas.hnu.edu.cn/cas/SbkLoginUrl.html?client_name=SbkClient"><i class="fa fa-qq"></i></a>-->
								<!--<a href="javascript:void(0);"><i class="fa fa-qq"></i></a>
                                <a href="javascript:void(0);"><i class="fa fa-weixin"></i></a>-->
							</div>
						</div>
					</div>

				</div>
				<div class="qcode" id="qcodepc" style="display:none">
					<p>统一身份认证</p>
					<div id="qrcode">
						<div id="qrcode-img">
							<img id="qrcodeImg" alt="Scan me!" style="display: none;">
						</div>
					</div>
					<div id="qrcode-content">
						<div class="qrcode-bg"></div>
						<p class="refresh-text">

							<a href="#" id="qrcode_refresh"><i class="fa fa-refresh"></i>刷新 </a>
						</p>
					</div>
					<!--		扫码成功提示模态框			-->
					<div id="qrcode-success" style="width: 190px;height: 190px;position: absolute;top: 62px;left: 50%;margin-left: -95px;background: rgba(255, 255, 255, 0.9);text-align: center;display: none;">
						<div style="width: 106px; height: 106px; margin: 0 auto; margin-top: 28px">
							<img id="qrcodeSuccess" src="images/smsuccess.png" style="width: 106px; font-size: 0;">
						</div>

						<span class="success-text" style="display: inline-block; color: #000; font-size: 18px; font-weight: 700">扫码成功</span>
					</div>
					<span>打开正方云app<br />在【首页】页面左上角打开扫一扫</span>
				</div>
			</div>
		</div>
	</div>
	

<div class="rwm" style="padding-top: 90px;width: 350px;float: right;height: 400px;overflow: hidden;">
	<h4 style="text-align: center;">微信扫码登录</h4>
	<div id="wxLogin"></div>
</div>
<!--
<div class="rwm" style="padding-top:90px;"><iframe id="wxFrame" src="https://open.weixin.qq.com/connect/qrconnect?appid=wx6bec8ae941e46eef&redirect_uri=http%3A%2F%2Fcas.hnu.edu.cn%2Fcas%2Flogin%3Fclient_name%3DWeiXinClient%26service%3Dhttps%3A%2F%2Fpt.hnu.edu.cn%2F&response_type=code&scope=snsapi_login#wechat_redirect
				"></iframe></div>
  -->
</div>
	<div class="footer">
               <p>湖南大学版权所有©2020年    通讯地址:湖南省长沙市岳麓区麓山南路麓山门    邮编：410082   </p>
               <p>湖南大学 湘ICP备09007699号 湘教QS3-200503-000481 湘教QS4-201312-010059 技术服务电话：88821520</p>
            </div>
</div>
<script type="text/javascript" src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
<script>

	$(function () {
		    var url = "http://cas.hnu.edu.cn/cas/login?client_name=WeiXinClient";
		    var service = GetQueryString("service");
		    console.log('service: ', service);
		    if (service) {
				  url = url + '&service='+service;
			}
			var wxObj = new WxLogin({
				self_redirect: false,
				id: "wxLogin", 
				appid: "wx6bec8ae941e46eef", 
				scope: "snsapi_login", 
				redirect_uri: url,
				stylelite: 1
			});

		
		/*
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#wxFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=WeiXinClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#wxFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('wxFrame').href=$('#wxFrame').attr('href');
			},100);
		}
		*/
	})
	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#qqFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("&response_type");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href + "&client_name=QQClient");
			var srcStr = pre + "&redirect_uri=" + ser + endStr;
			$('#qqFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('qqFrame').href=$('#qqFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if (service != null) {
			var wxurl = $("#ddFrame").attr('href');
			var start = wxurl.indexOf("&redirect_uri");
			var end = wxurl.indexOf("client_name");
			var pre = wxurl.substr(0, start);
			var endStr = wxurl.substr(end, wxurl.length);
			var ser = encodeURIComponent(window.location.href );
			var srcStr = pre + "&redirect_uri=" + ser + "%26" + endStr;
			$('#ddFrame').attr('href', srcStr);
			setTimeout(function (){
				document.getElementById('ddFrame').href=$('#ddFrame').attr('href');
			},100);
		}
	})

	$(function () {
		var service = GetQueryString("service");
		if(service!=null){
			$('#txwx').attr('href','TxWxLogin_pc.html?service='+service);
		}
	});
	function GetQueryString(name) {
		var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
		var r = window.location.search.substr(1).match(reg);
		if(r!=null){
			return  unescape(r[2]);
		}else{
			return null;
		}
	}
</script>
</body>

</html>