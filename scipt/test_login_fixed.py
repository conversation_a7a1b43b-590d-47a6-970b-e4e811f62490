#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复的登录测试脚本 - 解决Cookie获取问题
"""

import requests
import re
import os
import time

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def test_complete_login_flow():
    """测试完整的登录流程"""
    print("=== 完整登录流程测试 ===")
    
    session = requests.Session()
    
    # 禁用代理
    session.proxies = {
        'http': None,
        'https': None
    }
    
    # 设置通用headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1"
    })
    
    try:
        # 步骤1：获取新的登录页面
        print("\n1. 获取新的登录页面...")
        start_time = time.time()
        
        response = session.get(login_url, timeout=15)
        response.raise_for_status()
        
        print(f"状态码: {response.status_code}")
        print(f"URL: {response.url}")
        print(f"获取页面耗时: {time.time() - start_time:.2f}秒")
        
        # 检查页面是否有效
        if "闲置时间过长" in response.text or "请打开新页面" in response.text:
            print("❌ 页面已过期，无法继续")
            return False
        
        # 步骤2：快速解析参数并提交
        print("\n2. 解析参数...")
        
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        event_id_match = re.search(r'name="_eventId" value="([^"]*)"', response.text)
        
        if not execution_match:
            print("❌ 未找到execution参数")
            return False
        
        execution = execution_match.group(1)
        event_id = event_id_match.group(1) if event_id_match else "submit"
        
        print(f"execution: {execution[:50]}...")
        print(f"_eventId: {event_id}")
        
        # 步骤3：立即提交登录（减少延迟）
        print("\n3. 立即提交登录...")
        submit_start = time.time()
        
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": event_id,
            "geolocation": ""
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_url,
            "Origin": "http://cas.hnu.edu.cn"
        }
        
        login_response = session.post(
            login_url,
            data=login_data,
            headers=headers,
            timeout=15,
            allow_redirects=True
        )
        
        print(f"提交登录耗时: {time.time() - submit_start:.2f}秒")
        print(f"登录响应状态码: {login_response.status_code}")
        print(f"最终URL: {login_response.url}")
        
        # 步骤4：检查登录结果
        print("\n4. 检查登录结果...")
        
        # 检查是否成功重定向到目标系统
        if "eportal.hnu.edu.cn" in login_response.url:
            print("✅ 登录成功 - 已重定向到目标系统")
            success = True
        else:
            print("❌ 登录失败 - 未重定向到目标系统")
            success = False
            
            # 检查错误信息
            if "闲置时间过长" in login_response.text:
                print("  错误原因: 页面闲置时间过长")
            elif "用户名或密码错误" in login_response.text:
                print("  错误原因: 用户名或密码错误")
            elif "验证码" in login_response.text:
                print("  错误原因: 需要验证码")
        
        # 步骤5：检查并保存Cookie
        print("\n5. 检查Cookie...")
        
        all_cookies = []
        for cookie in session.cookies:
            all_cookies.append(f"{cookie.name}={cookie.value}")
            print(f"  {cookie.name}={cookie.value} (Domain: {cookie.domain})")
        
        if all_cookies:
            cookie_str = "; ".join(all_cookies)
            print(f"\n完整Cookie: {cookie_str}")
            
            # 保存Cookie
            with open("cookies_fixed.txt", "w") as f:
                f.write(cookie_str)
            print("已保存Cookie到 cookies_fixed.txt")
            
            return success
        else:
            print("❌ 未获取到任何Cookie")
            return False
            
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_with_fixed_cookie():
    """使用修复的Cookie测试API"""
    print("\n=== API测试（使用修复的Cookie）===")
    
    # 读取Cookie
    if not os.path.exists("cookies_fixed.txt"):
        print("❌ 未找到Cookie文件")
        return False
    
    with open("cookies_fixed.txt", "r") as f:
        cookie_str = f.read().strip()
    
    print(f"使用Cookie: {cookie_str[:100]}...")
    
    # 测试API
    url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": "57",  # 楼下篮球馆
        "start_time": "2025-01-10",
        "end_time": "2025-01-10"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": cookie_str,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ API调用成功")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('m', '未知错误')}")
            except:
                print("❌ 响应不是有效的JSON")
        
        return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def analyze_login_issue():
    """分析登录问题"""
    print("\n=== 登录问题分析 ===")
    
    print("可能的问题原因:")
    print("1. CAS session超时 - execution参数有时效性")
    print("2. 需要完整的重定向流程 - 单纯获取CAS cookie不够")
    print("3. 目标系统需要特定的session cookie")
    print("4. 可能需要处理多次重定向")
    
    print("\n建议的解决方案:")
    print("1. 减少获取参数到提交登录的时间间隔")
    print("2. 跟踪完整的重定向链")
    print("3. 确保获取目标系统的session cookie")
    print("4. 检查是否需要额外的验证步骤")

if __name__ == "__main__":
    # 运行修复的登录测试
    login_success = test_complete_login_flow()
    
    if login_success:
        # 如果登录成功，测试API
        test_api_with_fixed_cookie()
    else:
        # 分析问题
        analyze_login_issue()
    
    print("\n=== 测试完成 ===")
