#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录测试脚本 - 用于诊断Cookie获取问题
"""

import requests
import re
import os

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def test_login():
    """测试登录流程"""
    print("=== 登录测试开始 ===")

    session = requests.Session()

    # 禁用代理
    session.proxies = {
        'http': None,
        'https': None
    }

    try:
        # 步骤1：访问登录页面
        print("\n1. 访问登录页面...")
        response = session.get(login_url, timeout=15)
        print(f"状态码: {response.status_code}")
        print(f"URL: {response.url}")
        print(f"内容长度: {len(response.text)}")

        # 检查是否有错误信息
        if "闲置时间过长" in response.text or "请打开新页面" in response.text:
            print("⚠️ 检测到页面过期，重新获取登录页面...")
            # 清除cookies并重新请求
            session.cookies.clear()
            response = session.get(login_url, timeout=15)
            print(f"重新获取后状态码: {response.status_code}")

        # 保存页面内容
        with open("login_page.html", "w", encoding="utf-8") as f:
            f.write(response.text)
        print("已保存登录页面到 login_page.html")

        # 步骤2：解析表单参数
        print("\n2. 解析表单参数...")

        # 提取关键参数
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        event_id_match = re.search(r'name="_eventId" value="([^"]*)"', response.text)
        lt_match = re.search(r'name="lt" value="([^"]*)"', response.text)

        if not execution_match:
            print("❌ 未找到execution参数")
            # 查找所有隐藏字段进行调试
            hidden_fields = re.findall(r'<input[^>]*type=["\']hidden["\'][^>]*>', response.text)
            print("隐藏字段:")
            for field in hidden_fields[:10]:  # 只显示前10个
                print(f"  {field}")
            return False

        execution = execution_match.group(1)
        event_id = event_id_match.group(1) if event_id_match else "submit"
        lt = lt_match.group(1) if lt_match else None

        print(f"execution: {execution[:50]}...")  # 只显示前50个字符
        print(f"_eventId: {event_id}")
        print(f"lt: {lt}")

        # 步骤3：立即提交登录表单（避免session过期）
        print("\n3. 提交登录表单...")

        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": event_id,
            "geolocation": ""
        }

        if lt:
            login_data["lt"] = lt

        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": login_url,
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "max-age=0",
            "Upgrade-Insecure-Requests": "1"
        }

        print(f"登录数据: {dict((k, v if k != 'execution' else v[:20]+'...' if len(v) > 20 else v) for k, v in login_data.items())}")

        login_response = session.post(
            login_url,
            data=login_data,
            headers=headers,
            timeout=15,
            allow_redirects=True
        )

        print(f"登录响应状态码: {login_response.status_code}")
        print(f"最终URL: {login_response.url}")
        print(f"响应内容长度: {len(login_response.text)}")

        # 保存响应内容
        with open("login_response.html", "w", encoding="utf-8") as f:
            f.write(login_response.text)
        print("已保存登录响应到 login_response.html")

        # 步骤4：检查登录结果
        print("\n4. 检查登录结果...")

        # 检查URL变化
        if "eportal.hnu.edu.cn" in login_response.url:
            print("✅ 登录成功 - 已重定向到目标系统")
            success = True
        elif "cas.hnu.edu.cn" in login_response.url and "login" in login_response.url:
            print("❌ 登录失败 - 仍在CAS登录页面")
            success = False

            # 检查错误信息
            if "用户名或密码错误" in login_response.text:
                print("  错误原因: 用户名或密码错误")
            elif "验证码" in login_response.text:
                print("  错误原因: 需要验证码")
            elif "账号被锁定" in login_response.text:
                print("  错误原因: 账号被锁定")
            elif "闲置时间过长" in login_response.text:
                print("  错误原因: 页面闲置时间过长")
            else:
                print("  未知错误，请检查响应内容")
        else:
            print(f"⚠️ 未知重定向: {login_response.url}")
            success = False

        # 步骤5：检查Cookie
        print("\n5. 检查Cookie...")

        print(f"Session中的Cookie数量: {len(session.cookies)}")
        for cookie in session.cookies:
            print(f"  {cookie.name}={cookie.value}")
            print(f"    Domain: {cookie.domain}")
            print(f"    Path: {cookie.path}")
            print(f"    Secure: {cookie.secure}")

        # 构建Cookie字符串
        if session.cookies:
            cookie_str = "; ".join([f"{c.name}={c.value}" for c in session.cookies])
            print(f"\nCookie字符串: {cookie_str}")

            # 保存Cookie
            with open("cookies.txt", "w") as f:
                f.write(cookie_str)
            print("已保存Cookie到 cookies.txt")

            return success
        else:
            print("❌ 未获取到任何Cookie")
            return False

    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_with_cookie():
    """使用获取的Cookie测试API"""
    print("\n=== API测试开始 ===")
    
    # 读取Cookie
    if not os.path.exists("cookies.txt"):
        print("❌ 未找到Cookie文件")
        return False
    
    with open("cookies.txt", "r") as f:
        cookie_str = f.read().strip()
    
    print(f"使用Cookie: {cookie_str[:100]}...")
    
    # 测试API
    url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": "57",  # 楼下篮球馆
        "start_time": "2025-01-10",
        "end_time": "2025-01-10"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": cookie_str,
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应内容: {response.text[:500]}...")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ API调用成功")
                    return True
                else:
                    print(f"❌ API返回错误: {data.get('m', '未知错误')}")
            except:
                print("❌ 响应不是有效的JSON")
        
        return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行登录测试
    login_success = test_login()
    
    if login_success:
        # 如果登录成功，测试API
        test_api_with_cookie()
    
    print("\n=== 测试完成 ===")
