<!DOCTYPE html>






<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="assets/plugins/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="assets/css/base.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="assets/css/zftal-ui.css" />
    <link rel="stylesheet" href="assets/css/no_auth.css">
    <title>统一身份认证平台</title>
    <script type='text/javascript' src="js/jquery/jquery-1.11.1-min.js"></script>
    <script src="js/jquery.countdown.js"></script>
</head>
<body>
<img src="assets/images/bg_noauth.jpg" alt="background-img" class="img-responsive bg-error">
<div class="header">
    <div class="logo">
        <ul>
            <li>统一身份认证平台</li>
        </ul>
    </div>
</div>
<div class="content">
    <div class="text">
        <h4 class="bigf">403 - 访问被拒绝</h4>
        

        
            
                <h4>输错密码次数太多或当前账号登录次数超过设置次数，账号被锁定。</h4>
            
        

        
            
                <h4 id="time-box"></h4>
            
        

        
        <div class="div-group">
        </div>
    </div>
</div>
<script>
    if ($("#time-box")) {
        var allowLoginTime = '2025-07-09 11:59:21';
        if (allowLoginTime && allowLoginTime != '') {
            var reg = new RegExp('-', "g" )
            var time = allowLoginTime.replace( reg , '/' );
            allowLoginTime = new Date(time);
            $("#time-box").countdown(allowLoginTime,function (event) {
                //时间格式
                var format = event.strftime('访问倒计时: %H时 %M分 %S秒');
                $("#time-box").html(format);
                //时间完成后回调时间
            }).on('finish.countdown',function () {
                window.location.href = 'https://cas.hnu.edu.cn/cas/login';
            });
        }
    }
</script>
</body>
</html>