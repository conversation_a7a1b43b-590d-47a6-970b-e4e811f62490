#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的CAS登录解决方案 - 支持验证码处理
"""

import requests
import re
import time
import os
from urllib.parse import urljoin, urlparse
from PIL import Image
import io

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def get_captcha_image(session, base_url):
    """获取验证码图片"""
    try:
        # 常见的验证码URL模式
        captcha_urls = [
            "/cas/captcha",
            "/cas/captcha.jpg",
            "/cas/kaptcha.jpg",
            "/cas/authcode",
            "/cas/verifyCode"
        ]
        
        for captcha_path in captcha_urls:
            captcha_url = urljoin(base_url, captcha_path)
            print(f"尝试获取验证码: {captcha_url}")
            
            try:
                response = session.get(captcha_url, timeout=10)
                if response.status_code == 200 and 'image' in response.headers.get('content-type', ''):
                    print(f"✅ 成功获取验证码图片")
                    
                    # 保存验证码图片
                    with open("captcha.jpg", "wb") as f:
                        f.write(response.content)
                    
                    # 尝试显示验证码图片
                    try:
                        img = Image.open(io.BytesIO(response.content))
                        img.show()
                        print("验证码图片已显示，请查看")
                    except:
                        print("验证码图片已保存到 captcha.jpg，请手动查看")
                    
                    return True
            except:
                continue
        
        print("❌ 无法获取验证码图片")
        return False
        
    except Exception as e:
        print(f"❌ 获取验证码时发生错误: {e}")
        return False

def complete_cas_login():
    """完整的CAS登录流程，支持验证码"""
    print("=== 完整CAS登录流程 ===")
    
    session = requests.Session()
    session.proxies = {'http': None, 'https': None}
    
    # 设置真实的浏览器headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1"
    })
    
    try:
        # 步骤1：获取登录页面
        print("\n1. 获取登录页面...")
        response = session.get(login_url, timeout=15)
        
        if response.status_code != 200:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False, None
        
        print(f"✅ 登录页面获取成功")
        base_url = f"{urlparse(response.url).scheme}://{urlparse(response.url).netloc}"
        
        # 步骤2：解析表单参数
        print("\n2. 解析表单参数...")
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        if not execution_match:
            print("❌ 未找到execution参数")
            return False, None
        
        execution = execution_match.group(1)
        event_id = "submit"
        
        print(f"✅ 获取到execution参数")
        
        # 步骤3：检查是否需要验证码
        print("\n3. 检查验证码要求...")
        need_captcha = False
        
        # 检查页面是否包含验证码相关元素
        if 'authcode' in response.text or 'captcha' in response.text or '验证码' in response.text:
            print("⚠️ 检测到需要验证码")
            need_captcha = True
            
            # 尝试获取验证码图片
            if get_captcha_image(session, base_url):
                captcha_code = input("请输入验证码: ").strip()
                if not captcha_code:
                    print("❌ 验证码不能为空")
                    return False, None
            else:
                print("❌ 无法获取验证码图片")
                return False, None
        else:
            print("✅ 无需验证码")
            captcha_code = None
        
        # 步骤4：构建登录数据
        print("\n4. 准备登录数据...")
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": event_id,
            "geolocation": ""
        }
        
        if need_captcha and captcha_code:
            login_data["authcode"] = captcha_code
        
        # 构建提交URL
        form_action_match = re.search(r'<form[^>]*action="([^"]*)"', response.text)
        if form_action_match:
            form_action = form_action_match.group(1).strip()
            if form_action.startswith('http'):
                submit_url = form_action
            elif form_action.startswith('/'):
                submit_url = urljoin(response.url, form_action)
            else:
                base_url_path = response.url.rsplit('/', 1)[0] + '/'
                submit_url = urljoin(base_url_path, form_action)
        else:
            submit_url = response.url
        
        print(f"提交URL: {submit_url}")
        
        # 步骤5：提交登录表单
        print("\n5. 提交登录表单...")
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": response.url,
            "Origin": base_url
        }
        
        login_response = session.post(
            submit_url,
            data=login_data,
            headers=headers,
            timeout=30,
            allow_redirects=True  # 允许自动重定向
        )
        
        print(f"登录响应状态码: {login_response.status_code}")
        print(f"最终URL: {login_response.url}")
        
        # 步骤6：检查登录结果
        print("\n6. 检查登录结果...")
        
        # 检查是否成功重定向到目标系统
        if "eportal.hnu.edu.cn" in login_response.url:
            print("✅ 登录成功！已重定向到目标系统")
            success = True
        else:
            print("❌ 登录失败或未完成重定向")
            success = False
            
            # 检查错误信息
            if "用户名或密码错误" in login_response.text:
                print("  错误: 用户名或密码错误")
            elif "验证码错误" in login_response.text or "验证码不正确" in login_response.text:
                print("  错误: 验证码错误")
            elif "账号被锁定" in login_response.text:
                print("  错误: 账号被锁定")
            else:
                print("  未知错误，请检查响应内容")
        
        # 步骤7：收集Cookie
        print("\n7. 收集Cookie...")
        all_cookies = []
        eportal_cookies = []
        
        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            all_cookies.append(cookie_str)
            
            print(f"Cookie: {cookie.name}={cookie.value[:20]}... (Domain: {cookie.domain})")
            
            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)
        
        # 保存Cookie
        if eportal_cookies:
            cookie_to_save = "; ".join(eportal_cookies)
            print("✅ 获取到目标系统Cookie")
        elif all_cookies:
            cookie_to_save = "; ".join(all_cookies)
            print("⚠️ 只获取到CAS Cookie")
        else:
            print("❌ 未获取到任何Cookie")
            return False
        
        # 保存到文件
        with open("final_cookies.txt", "w") as f:
            f.write(cookie_to_save)
        print(f"Cookie已保存到 final_cookies.txt")
        
        return success, cookie_to_save
        
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_final_cookie():
    """测试最终获取的Cookie"""
    print("\n=== 测试Cookie有效性 ===")
    
    if not os.path.exists("final_cookies.txt"):
        print("❌ 未找到Cookie文件")
        return False
    
    with open("final_cookies.txt", "r") as f:
        cookie_str = f.read().strip()
    
    print(f"测试Cookie: {cookie_str[:100]}...")
    
    # 测试API调用
    url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": "57",
        "start_time": "2025-01-10",
        "end_time": "2025-01-10"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": cookie_str,
        "Accept": "application/json, text/plain, */*",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ Cookie有效！API调用成功")
                    print(f"返回数据: {len(data.get('d', {}))}个资源")
                    return True
                else:
                    print(f"❌ Cookie无效: {data.get('m')}")
                    return False
            except:
                print("❌ 响应格式错误")
                return False
        else:
            print(f"❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    # 运行完整登录流程
    success, cookie = complete_cas_login()
    
    if success and cookie:
        # 测试Cookie有效性
        test_final_cookie()
    
    print("\n=== 登录流程完成 ===")
