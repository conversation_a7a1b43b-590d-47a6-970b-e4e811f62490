#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie获取问题诊断脚本
分析CAS登录流程中的具体问题
"""

import requests
import re
import time
from urllib.parse import urlparse, parse_qs, urljoin

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def analyze_cas_flow():
    """分析CAS登录流程"""
    print("=== CAS登录流程分析 ===")
    
    session = requests.Session()
    session.proxies = {'http': None, 'https': None}
    
    # 设置更真实的浏览器headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1"
    })
    
    try:
        # 步骤1：分析登录页面
        print("\n1. 获取登录页面...")
        response = session.get(login_url, timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"URL: {response.url}")
        print(f"响应头: {dict(response.headers)}")
        
        # 检查页面内容
        if response.status_code != 200:
            print(f"❌ 登录页面访问失败")
            return False
        
        # 分析表单参数
        print("\n2. 分析表单参数...")
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        event_id_match = re.search(r'name="_eventId" value="([^"]*)"', response.text)
        lt_match = re.search(r'name="lt" value="([^"]*)"', response.text)
        
        if not execution_match:
            print("❌ 未找到execution参数")
            return False
        
        execution = execution_match.group(1)
        event_id = event_id_match.group(1) if event_id_match else "submit"
        lt = lt_match.group(1) if lt_match else None
        
        print(f"execution: {execution[:50]}...")
        print(f"_eventId: {event_id}")
        print(f"lt: {lt}")
        
        # 检查表单action
        form_action_match = re.search(r'<form[^>]*action="([^"]*)"', response.text)
        if form_action_match:
            form_action = form_action_match.group(1).strip()
            print(f"表单action: {form_action}")

            # 构建完整的提交URL
            if form_action.startswith('http'):
                submit_url = form_action
            elif form_action.startswith('/'):
                submit_url = urljoin(response.url, form_action)
            else:
                # 相对路径，基于当前URL构建
                base_url = response.url.rsplit('/', 1)[0] + '/'
                submit_url = urljoin(base_url, form_action)
        else:
            submit_url = response.url

        print(f"提交URL: {submit_url}")
        
        # 步骤3：模拟真实的表单提交
        print("\n3. 提交登录表单...")
        
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": event_id,
            "geolocation": ""
        }
        
        if lt:
            login_data["lt"] = lt
        
        # 更新headers为表单提交
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": response.url,
            "Origin": "http://cas.hnu.edu.cn",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1"
        }
        
        # 提交表单，不自动跟随重定向
        login_response = session.post(
            submit_url,
            data=login_data,
            headers=headers,
            timeout=15,
            allow_redirects=False
        )
        
        print(f"登录响应状态码: {login_response.status_code}")
        print(f"响应头: {dict(login_response.headers)}")

        # 检查登录响应内容
        if login_response.status_code == 200:
            print("登录返回200，检查响应内容...")

            # 保存响应内容用于分析
            with open("login_response_analysis.html", "w", encoding="utf-8") as f:
                f.write(login_response.text)
            print("已保存登录响应到 login_response_analysis.html")

            # 检查是否有错误信息
            if "用户名或密码错误" in login_response.text:
                print("❌ 用户名或密码错误")
                return False
            elif "验证码" in login_response.text:
                print("❌ 需要验证码")
                return False
            elif "账号被锁定" in login_response.text:
                print("❌ 账号被锁定")
                return False
            elif "闲置时间过长" in login_response.text:
                print("❌ 页面闲置时间过长")
                return False

            # 检查是否有隐藏的重定向信息
            redirect_script = re.search(r'window\.location\.href\s*=\s*["\']([^"\']+)["\']', login_response.text)
            if redirect_script:
                redirect_url = redirect_script.group(1)
                print(f"发现JavaScript重定向: {redirect_url}")

                # 手动执行重定向
                try:
                    redirect_response = session.get(redirect_url, timeout=15, allow_redirects=False)
                    login_response = redirect_response
                    print(f"JavaScript重定向响应: {redirect_response.status_code}")
                except Exception as e:
                    print(f"JavaScript重定向失败: {e}")

        # 步骤4：手动处理重定向链
        print("\n4. 处理重定向链...")
        
        current_response = login_response
        redirect_count = 0
        max_redirects = 10
        
        while (current_response.status_code in [301, 302, 303, 307, 308] and 
               redirect_count < max_redirects):
            
            redirect_count += 1
            location = current_response.headers.get('Location')
            
            if not location:
                print(f"❌ 重定向{redirect_count}: 没有Location头")
                break
            
            # 处理相对URL
            if location.startswith('/'):
                location = urljoin(current_response.url, location)
            
            print(f"重定向{redirect_count}: {location}")
            
            # 检查当前Cookie状态
            print(f"  当前Cookie数量: {len(session.cookies)}")
            for cookie in session.cookies:
                print(f"    {cookie.name}={cookie.value[:20]}... (Domain: {cookie.domain})")
            
            # 跟随重定向
            try:
                current_response = session.get(location, timeout=15, allow_redirects=False)
                print(f"  响应状态码: {current_response.status_code}")
                
                # 检查是否到达目标域名
                parsed_url = urlparse(location)
                if "eportal.hnu.edu.cn" in parsed_url.netloc:
                    print(f"  ✅ 到达目标系统域名")
                    
            except Exception as e:
                print(f"  ❌ 重定向失败: {e}")
                break
        
        # 步骤5：分析最终状态
        print(f"\n5. 最终状态分析...")
        print(f"重定向次数: {redirect_count}")
        print(f"最终状态码: {current_response.status_code}")
        print(f"最终URL: {current_response.url}")
        
        # 分析Cookie
        print(f"\n6. Cookie分析...")
        all_cookies = []
        eportal_cookies = []
        cas_cookies = []
        
        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            all_cookies.append(cookie_str)
            
            print(f"Cookie: {cookie.name}={cookie.value}")
            print(f"  Domain: {cookie.domain}")
            print(f"  Path: {cookie.path}")
            print(f"  Secure: {cookie.secure}")
            print(f"  HttpOnly: {getattr(cookie, 'has_nonstandard_attr', lambda x: False)('HttpOnly')}")
            
            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)
            elif "cas.hnu.edu.cn" in cookie.domain:
                cas_cookies.append(cookie_str)
        
        # 保存不同类型的Cookie
        if all_cookies:
            with open("cookies_all.txt", "w") as f:
                f.write("; ".join(all_cookies))
            print(f"已保存所有Cookie到 cookies_all.txt")
        
        if eportal_cookies:
            with open("cookies_eportal_only.txt", "w") as f:
                f.write("; ".join(eportal_cookies))
            print(f"已保存目标系统Cookie到 cookies_eportal_only.txt")
            return True
        else:
            print(f"❌ 未获取到目标系统Cookie")
            
            # 如果只有CAS Cookie，说明重定向没有完成
            if cas_cookies:
                print(f"只获取到CAS Cookie，重定向流程可能不完整")
            
            return False
            
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_cookies():
    """测试不同Cookie的有效性"""
    print("\n=== Cookie有效性测试 ===")
    
    cookie_files = ["cookies_all.txt", "cookies_eportal_only.txt"]
    
    for cookie_file in cookie_files:
        if not os.path.exists(cookie_file):
            continue
            
        print(f"\n测试 {cookie_file}...")
        
        with open(cookie_file, "r") as f:
            cookie_str = f.read().strip()
        
        print(f"Cookie内容: {cookie_str[:100]}...")
        
        # 测试API调用
        url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
        params = {
            "resource_id": "57",
            "start_time": "2025-01-10",
            "end_time": "2025-01-10"
        }
        
        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": cookie_str,
            "Accept": "application/json, text/plain, */*",
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            print(f"API响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('e') == 0:
                        print(f"✅ {cookie_file} 有效")
                        return cookie_str
                    else:
                        print(f"❌ {cookie_file} 无效: {data.get('m')}")
                except:
                    print(f"❌ {cookie_file} 响应格式错误")
            else:
                print(f"❌ {cookie_file} API调用失败")
                
        except Exception as e:
            print(f"❌ {cookie_file} 测试失败: {e}")
    
    return None

if __name__ == "__main__":
    import os
    
    # 运行诊断
    success = analyze_cas_flow()
    
    if success:
        # 测试Cookie有效性
        valid_cookie = test_different_cookies()
        if valid_cookie:
            print(f"\n✅ 找到有效Cookie: {valid_cookie[:50]}...")
        else:
            print(f"\n❌ 所有Cookie都无效")
    
    print("\n=== 诊断完成 ===")
