#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的登录方案 - 解决时序和参数问题
"""

import requests
import re
import time
from urllib.parse import urljoin, urlparse

# 配置参数
login_url = "http://cas.hnu.edu.cn/cas/login?service=https%3A%2F%2Feportal.hnu.edu.cn%2Fsite%2Flogin%2Fcas-login%3Fredirect_url%3Dhttps%253A%252F%252Feportal.hnu.edu.cn%252Fv2%252Fsite%252Findex"
username = "S2410W1114"
password = "Owen@13876801105"

def optimized_login():
    """优化的登录流程"""
    print("=== 优化登录流程 ===")
    
    session = requests.Session()
    session.proxies = {'http': None, 'https': None}
    
    # 设置完整的浏览器headers
    session.headers.update({
        "User-Agent": "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "max-age=0",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Connection": "keep-alive"
    })
    
    try:
        # 步骤1：快速获取登录页面
        print("\n1. 快速获取登录页面...")
        start_time = time.time()
        
        response = session.get(login_url, timeout=10)
        
        if response.status_code != 200:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
        
        get_time = time.time() - start_time
        print(f"✅ 登录页面获取成功 ({get_time:.2f}秒)")
        
        # 步骤2：立即解析参数
        print("\n2. 解析参数...")
        parse_start = time.time()
        
        execution_match = re.search(r'name="execution" value="([^"]*)"', response.text)
        if not execution_match:
            print("❌ 未找到execution参数")
            return False
        
        execution = execution_match.group(1)
        
        # 获取表单action
        form_action_match = re.search(r'<form[^>]*action="([^"]*)"', response.text)
        if form_action_match:
            form_action = form_action_match.group(1).strip()
            if form_action.startswith('http'):
                submit_url = form_action
            elif form_action.startswith('/'):
                submit_url = urljoin(response.url, form_action)
            else:
                base_url_path = response.url.rsplit('/', 1)[0] + '/'
                submit_url = urljoin(base_url_path, form_action)
        else:
            submit_url = response.url
        
        parse_time = time.time() - parse_start
        print(f"✅ 参数解析完成 ({parse_time:.2f}秒)")
        print(f"execution: {execution[:50]}...")
        print(f"提交URL: {submit_url}")
        
        # 步骤3：立即提交登录（最小化延迟）
        print("\n3. 立即提交登录...")
        submit_start = time.time()
        
        login_data = {
            "username": username,
            "password": password,
            "execution": execution,
            "_eventId": "submit",
            "geolocation": ""
        }
        
        # 优化的提交headers
        submit_headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Referer": response.url,
            "Origin": f"{urlparse(response.url).scheme}://{urlparse(response.url).netloc}",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-User": "?1"
        }
        
        # 提交登录表单，允许重定向
        login_response = session.post(
            submit_url,
            data=login_data,
            headers=submit_headers,
            timeout=20,
            allow_redirects=True
        )
        
        submit_time = time.time() - submit_start
        total_time = time.time() - start_time
        
        print(f"✅ 登录提交完成 ({submit_time:.2f}秒)")
        print(f"总耗时: {total_time:.2f}秒")
        
        # 步骤4：分析登录结果
        print(f"\n4. 分析登录结果...")
        print(f"最终状态码: {login_response.status_code}")
        print(f"最终URL: {login_response.url}")
        
        # 检查是否成功
        success = False
        if "eportal.hnu.edu.cn" in login_response.url:
            print("✅ 登录成功！已重定向到目标系统")
            success = True
        else:
            print("❌ 登录失败或未完成重定向")
            
            # 检查具体错误
            if login_response.status_code == 200:
                # 保存响应用于分析
                with open("optimized_login_response.html", "w", encoding="utf-8") as f:
                    f.write(login_response.text)
                
                if "用户名或密码错误" in login_response.text:
                    print("  错误: 用户名或密码错误")
                elif "验证码" in login_response.text:
                    print("  错误: 需要验证码")
                elif "账号被锁定" in login_response.text:
                    print("  错误: 账号被锁定")
                elif "闲置时间过长" in login_response.text:
                    print("  错误: 页面闲置时间过长")
                else:
                    print("  未知错误")
        
        # 步骤5：收集Cookie
        print(f"\n5. 收集Cookie...")
        all_cookies = []
        eportal_cookies = []
        
        for cookie in session.cookies:
            cookie_str = f"{cookie.name}={cookie.value}"
            all_cookies.append(cookie_str)
            
            print(f"Cookie: {cookie.name}={cookie.value[:30]}... (Domain: {cookie.domain})")
            
            if "eportal.hnu.edu.cn" in cookie.domain:
                eportal_cookies.append(cookie_str)
        
        # 保存Cookie
        if eportal_cookies:
            final_cookie = "; ".join(eportal_cookies)
            print("✅ 获取到目标系统Cookie")
        elif all_cookies:
            final_cookie = "; ".join(all_cookies)
            print("⚠️ 只获取到CAS Cookie")
        else:
            print("❌ 未获取到任何Cookie")
            return False
        
        # 保存到文件
        with open("optimized_cookies.txt", "w") as f:
            f.write(final_cookie)
        print(f"Cookie已保存到 optimized_cookies.txt")
        
        return success, final_cookie
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 登录过程中发生错误: {e}")
        return False

def test_optimized_cookie():
    """测试优化后的Cookie"""
    print("\n=== 测试优化Cookie ===")
    
    try:
        with open("optimized_cookies.txt", "r") as f:
            cookie_str = f.read().strip()
    except:
        print("❌ 未找到Cookie文件")
        return False
    
    print(f"测试Cookie: {cookie_str[:100]}...")
    
    # 测试API调用
    url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
    params = {
        "resource_id": "57",
        "start_time": "2025-01-10",
        "end_time": "2025-01-10"
    }
    
    headers = {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Cookie": cookie_str,
        "Accept": "application/json, text/plain, */*",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ Cookie有效！API调用成功")
                    print(f"返回数据: {len(data.get('d', {}))}个资源")
                    return True
                else:
                    print(f"❌ Cookie无效: {data.get('m')}")
                    return False
            except:
                print("❌ 响应格式错误")
                print(f"响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def try_alternative_approach():
    """尝试替代方案"""
    print("\n=== 尝试替代方案 ===")
    print("如果直接登录失败，可以尝试以下方案：")
    print("1. 手动登录获取Cookie")
    print("2. 使用浏览器开发者工具复制Cookie")
    print("3. 等待账号解锁后重试")
    print("4. 联系系统管理员")
    
    # 提供手动Cookie输入选项
    manual_cookie = input("\n如果您有有效的Cookie，请粘贴到这里（或按回车跳过）: ").strip()
    if manual_cookie:
        with open("manual_cookies.txt", "w") as f:
            f.write(manual_cookie)
        print("手动Cookie已保存到 manual_cookies.txt")
        
        # 测试手动Cookie
        print("测试手动输入的Cookie...")
        url = "https://eportal.hnu.edu.cn/site/reservation/resource-info-margin"
        params = {"resource_id": "57", "start_time": "2025-01-10", "end_time": "2025-01-10"}
        headers = {
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Cookie": manual_cookie,
            "Accept": "application/json, text/plain, */*",
            "X-Requested-With": "XMLHttpRequest",
            "Referer": "https://eportal.hnu.edu.cn/v2/site/reservation"
        }
        
        try:
            response = requests.get(url, params=params, headers=headers, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('e') == 0:
                    print("✅ 手动Cookie有效！")
                    return True
                else:
                    print(f"❌ 手动Cookie无效: {data.get('m')}")
            else:
                print(f"❌ 手动Cookie测试失败")
        except Exception as e:
            print(f"❌ 手动Cookie测试错误: {e}")
    
    return False

if __name__ == "__main__":
    # 运行优化登录流程
    result = optimized_login()
    
    if isinstance(result, tuple):
        success, cookie = result
        if success:
            test_optimized_cookie()
        else:
            try_alternative_approach()
    else:
        try_alternative_approach()
    
    print("\n=== 优化登录完成 ===")
