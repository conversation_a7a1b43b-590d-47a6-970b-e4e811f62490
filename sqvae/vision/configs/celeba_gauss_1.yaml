path_specific: "celeba_sqvae_gaussian_1/"
  
dataset:
  name: 'CelebA'
  shape: (3, 64, 64)
  dim_x: 12288 # 3 * 64 * 64

model:
  name: "GaussianSQVAE"
  log_param_q_init: 2.995732273553991 # log(20.0), larger/smaller vaulue is recommended for larger/smaller code dimension
  param_var_q: "gaussian_1"

network:
  name: "resnet"
  num_rb: 6

train:
  epoch_max: 70

quantization:
  size_dict: 512
  dim_dict: 64
