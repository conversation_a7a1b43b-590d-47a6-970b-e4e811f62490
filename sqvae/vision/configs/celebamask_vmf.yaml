path_specific: "celebamask_sqvae_vmf/"
  
dataset:
  name: 'CelebAMask_HQ'
  shape: (1, 64, 64)
  dim_x: 4096 # 1 * 64 * 64

model:
  name: "VmfSQVAE"
  log_kappa_inv: -4.605170185988091 # log(0.01)
  log_param_q_init: -2.995732273553991 # log(0.05), smaller/larger vaulue is recommended for larger/smaller code dimension
  param_var_q: "vmf"

network:
  name: "resnet_label"
  num_rb: 2
  num_class: 19
  act_decoder: "exp"

train:
  epoch_max: 70

quantization:
  size_dict: 64
  dim_dict: 64
